import os
import ast
import numpy as np
import torch

from apps.analysis.breathe.sleep.model.model import EnhancedSleepModel
from apps.analysis.breathe.sleep.model.preprocess import (
    extract_ecg_features,
    extract_resp_features,
)

LABELS_3 = ['W', 'N1-3', 'R']


def parse_array(data) -> np.ndarray:
    try:
        if isinstance(data, np.ndarray):
            return data.astype(float).flatten()
        if isinstance(data, (list, tuple)):
            return np.array(data, dtype=float).flatten()
        if isinstance(data, str):
            s = data.strip()
            try:
                parsed = ast.literal_eval(s)
                return np.array(parsed, dtype=float).flatten()
            except Exception:
                parts = [p for p in s.replace('\n', ',').split(',') if p.strip()]
                return np.array([float(x) for x in parts], dtype=float)
    except Exception:
        pass
    return np.array([], dtype=float)


def _resolve_weights_path(custom_path: str = None) -> str:
    candidates = []
    if custom_path:
        candidates.append(custom_path)

    # 获取当前文件所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sleep_model_dir = os.path.join(current_dir, 'sleep', 'model')

    candidates += [
        os.path.join(sleep_model_dir, 'finetuned.pth'),
        os.path.join(sleep_model_dir, 'best_model.pth'),
    ]

    for p in candidates:
        if p and os.path.exists(p):
            return p
    return None


def _standardize(feat: np.ndarray, mean_arr: np.ndarray, std_arr: np.ndarray) -> np.ndarray:
    std_arr = np.where(std_arr == 0, 1.0, std_arr)
    return (feat - mean_arr) / std_arr


def run_sleep_inference(
    ecg_data,
    ecg_fs: float,
    breathe_data,
    breathe_fs: float = 12.5,
    weights_path: str = None,
):
    """
    执行睡眠三分类推理（W/N1-3/R），窗口=30秒，聚合=最后一个完整窗口。
    返回字典：{
        'windows': [{'index': i, 'probs': [pW,pN,pR], 'pred': int, 'label': str}, ...],
        'aggregate': {'pred': int, 'label': str},
        'meta': {'weights_path': path, 'num_windows': n}
    }
    不修改任何现有接口；调用方可在需要时引入。
    """
    ecg = parse_array(ecg_data)
    resp = parse_array(breathe_data)
    if ecg.size == 0 or resp.size == 0 or ecg_fs is None or ecg_fs <= 0 or breathe_fs is None or breathe_fs <= 0:
        return {'windows': [], 'aggregate': {'pred': -1, 'label': ''}, 'meta': {'weights_path': None, 'num_windows': 0}}

    win_sec = 30.0
    ecg_len = int(ecg_fs * win_sec)
    resp_len = int(breathe_fs * win_sec)
    num_windows = min(len(ecg) // ecg_len, len(resp) // resp_len)
    if num_windows <= 0:
        return {'windows': [], 'aggregate': {'pred': -1, 'label': ''}, 'meta': {'weights_path': None, 'num_windows': 0}}

    resolved = _resolve_weights_path(weights_path)
    if not resolved:
        return {'windows': [], 'aggregate': {'pred': -1, 'label': ''}, 'meta': {'weights_path': None, 'num_windows': num_windows}}

    ckpt = torch.load(resolved, map_location='cpu')
    ecg_feat_size = int(ckpt.get('ecg_feat_size', 12))
    resp_feat_size = int(ckpt.get('resp_feat_size', 8))
    ecg_mean = np.array(ckpt.get('ecg_mean', np.zeros(ecg_feat_size)), dtype=float).reshape(1, -1)
    ecg_std = np.array(ckpt.get('ecg_std', np.ones(ecg_feat_size)), dtype=float).reshape(1, -1)
    resp_mean = np.array(ckpt.get('resp_mean', np.zeros(resp_feat_size)), dtype=float).reshape(1, -1)
    resp_std = np.array(ckpt.get('resp_std', np.ones(resp_feat_size)), dtype=float).reshape(1, -1)

    model = EnhancedSleepModel(ecg_feat_size=ecg_feat_size, resp_feat_size=resp_feat_size, num_classes=3)
    model.load_state_dict(ckpt['state_dict'])
    model.eval()

    results = []
    with torch.no_grad():
        for i in range(num_windows):
            ecg_seg = ecg[i * ecg_len:(i + 1) * ecg_len]
            resp_seg = resp[i * resp_len:(i + 1) * resp_len]

            ecg_feat = np.array(extract_ecg_features(ecg_seg, ecg_fs), dtype=float).reshape(1, -1)
            resp_feat = np.array(extract_resp_features(resp_seg, breathe_fs), dtype=float).reshape(1, -1)

            if ecg_feat.shape[1] < ecg_feat_size:
                pad = np.zeros((1, ecg_feat_size - ecg_feat.shape[1]), dtype=float)
                ecg_feat = np.concatenate([ecg_feat, pad], axis=1)
            else:
                ecg_feat = ecg_feat[:, :ecg_feat_size]

            if resp_feat.shape[1] < resp_feat_size:
                pad = np.zeros((1, resp_feat_size - resp_feat.shape[1]), dtype=float)
                resp_feat = np.concatenate([resp_feat, pad], axis=1)
            else:
                resp_feat = resp_feat[:, :resp_feat_size]

            ecg_z = _standardize(ecg_feat, ecg_mean, ecg_std)
            resp_z = _standardize(resp_feat, resp_mean, resp_std)

            ecg_t = torch.tensor(ecg_z, dtype=torch.float32)
            resp_t = torch.tensor(resp_z, dtype=torch.float32)

            logits = model(ecg_t, resp_t)
            probs = torch.softmax(logits, dim=1).cpu().numpy().reshape(-1).tolist()
            pred = int(np.argmax(probs))
            results.append({'index': i, 'probs': probs, 'pred': pred, 'label': LABELS_3[pred]})

    aggregate = results[-1]
    return {
        'windows': results,
        'aggregate': {'pred': int(aggregate['pred']), 'label': str(aggregate['label'])},
        'meta': {'weights_path': resolved, 'num_windows': int(num_windows)}
    }

