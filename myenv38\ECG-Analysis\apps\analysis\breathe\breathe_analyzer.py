import ast
import numpy as np
from scipy.signal import find_peaks


def parse_breathe_data(data):
    """呼吸波数据转换为numpy数组"""
    try:
        if isinstance(data, (list, tuple, np.ndarray)):
            if len(data) > 0 and isinstance(data[0], str):
                float_data = [float(x) for x in data]
                result = np.array(float_data, dtype=float).flatten()
            else:
                result = np.array(data, dtype=float).flatten()
            return result
        if isinstance(data, str):
            s = data.strip()
            try:
                parsed = ast.literal_eval(s)
                return np.array(parsed, dtype=float).flatten()
            except Exception:
                parts = [p for p in s.replace('\n', ',').split(',') if p.strip()]
                return np.array([float(x) for x in parts], dtype=float)
    except Exception:
        pass
    return np.array([], dtype=float)


def compute_respiratory_rate(sig: np.ndarray, fs: float) -> int:
    """依据呼吸峰计数计算呼吸频率（次/分钟）"""
    if sig is None or sig.size == 0 or fs is None or fs <= 0:
        return 0

    duration_sec = sig.size / fs
    if duration_sec <= 0:
        return 0

    # 归一化
    sig = sig - float(np.nanmean(sig))
    std = float(np.nanstd(sig)) + 1e-8
    sig = sig / std

    distance = max(1, int(fs * 1.2))
    peaks, _ = find_peaks(sig, distance=distance)

    rate = (len(peaks) / duration_sec) * 60.0
    if not np.isfinite(rate):
        return 0

    rate = max(10.0, min(rate, 25.0))
    return int(round(rate))


def analyze_breathe_data(data, breathe_fs):
    """分析呼吸波数据，返回呼吸频率"""
    try:
        arr = parse_breathe_data(data)
        rate = compute_respiratory_rate(arr, breathe_fs)
        return rate
    except Exception:
        return 0
