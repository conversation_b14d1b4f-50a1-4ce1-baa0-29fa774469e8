import traceback

import numpy as np
from scipy.signal import find_peaks
from scipy.spatial.distance import euclidean
from fastdtw import fastdtw

from apps.utils.logger_helper import Logger
from apps.analysis.arrhythmia_diagnosis.af.af_utils import analyze_rr_intervals



# --- Helper functions for robust/adaptive thresholds ---
def _mad(x):
    x = np.asarray(x)
    if x.size == 0:
        return 1e-8
    m = np.median(np.abs(x - np.median(x)))
    return m if m > 1e-8 else 1e-8


def _adaptive_peak_height(seg, k=2.5):
    return k * _mad(seg)


def _local_median(vals, idx, w=5):
    left = max(0, idx - w)
    right = min(len(vals), idx + w)
    window = vals[left:right]
    if len(window) == 0:
        return float(np.median(vals))
    return float(np.median(window))


def _local_mad(vals, idx, w=5):
    left = max(0, idx - w)
    right = min(len(vals), idx + w)
    window = np.asarray(vals[left:right])
    if window.size == 0:
        window = np.asarray(vals)
    return _mad(window)




def process(signal_segment, sampling_rate, waveform_info):
    """
    心电信号PAC检测
    :param signal_segment: 经过 gain/zero 调整并切割后的有效信号段
    :param waveform_info: *原始信号*计算出的完整波形信息 (来自 get_waveform)
    :param sampling_rate: 采样率
    :return: True or False
    """
    try:
        waveform = waveform_info.get('waveform', {})
        if not waveform:
            return False

        rr_features = analyze_rr_intervals(waveform)
        # 更严格的房颤预筛选条件
        if (rr_features['rr_cv'] > 0.10 and rr_features['rr_irregularity'] > 0.07) or \
           (rr_features['rr_cv'] > 0.13 and rr_features['rr_irregularity'] > 0.05) or \
           (rr_features['rr_cv'] > 0.16):  # 极高的变异性直接排除
            Logger().info(f"心律高度不齐（CV={rr_features['rr_cv']:.3f}, Irreg={rr_features['rr_irregularity']:.3f}），疑似房颤，跳过PAC诊断。")
            return False

        rr_intervals = waveform['rr_intervals']
        if len(rr_intervals) < 3:
            return False

        q_indices_hamilton = waveform['q_peaks']
        s_indices_hamilton = waveform['s_peaks']
        rpeaks = waveform['r_peaks']
        p_positions = waveform['p_peaks']

        median_rr = np.median(rr_intervals)
        window_size = int(0.2 * sampling_rate)

        qrs_widths = []
        qrs_widths_adaptive = []

        for i in range(len(rpeaks)):
            q_idx = -1
            s_idx = -1
            if i < len(q_indices_hamilton):
                q_idx = q_indices_hamilton[i]
            if i < len(s_indices_hamilton):
                s_idx = s_indices_hamilton[i]

            if q_idx != -1 and s_idx != -1 and s_idx > q_idx:
                qrs_width = (s_idx - q_idx) / sampling_rate
                if 0.05 < qrs_width < 0.2:
                    qrs_widths.append(qrs_width)

        for i in range(len(rpeaks)):
            if rpeaks[i] - window_size < 0 or rpeaks[i] + window_size >= len(signal_segment):
                continue

            beat_window = signal_segment[rpeaks[i] - window_size:rpeaks[i] + window_size]

            energy = np.square(beat_window)
            threshold = 0.2 * np.max(energy)
            qrs_start = rpeaks[i] - window_size
            qrs_end = rpeaks[i] + window_size - 1

            for j in range(len(energy) // 2, 0, -1):
                if energy[j] < threshold:
                    qrs_start = rpeaks[i] - window_size + j
                    break

            for j in range(len(energy) // 2, len(energy) - 1):
                if energy[j] < threshold:
                    qrs_end = rpeaks[i] - window_size + j
                    break

            qrs_width_adaptive = (qrs_end - qrs_start) / sampling_rate
            if 0.05 < qrs_width_adaptive < 0.2:
                qrs_widths_adaptive.append(qrs_width_adaptive)

        if qrs_widths_adaptive:
            normal_qrs_width = np.median(qrs_widths_adaptive)
        elif qrs_widths:
            normal_qrs_width = np.median(qrs_widths)
        else:
            normal_qrs_width = 0.08

        pac_count = 0
        pac_beats = []
        borderline_pac_beats = []
        templates = []

        for i in range(len(rpeaks)):
            if i > 0 and i < len(rr_intervals) and rpeaks[i] - window_size >= 0 and rpeaks[i] + window_size < len(
                    signal_segment):
                if 0.9 * median_rr <= rr_intervals[i - 1] <= 1.1 * median_rr:  # 正常RR间期
                    beat = signal_segment[rpeaks[i] - window_size:rpeaks[i] + window_size]
                    if len(beat) == 2 * window_size:
                        templates.append(beat)

                        # 只收集最多10个模板
                        if len(templates) >= 10:
                            break


        if len(templates) < 3:
            Logger().warning("收集到的正常心搏模板不足，使用替代方法")

            all_beats = []
            for i in range(len(rpeaks)):
                if rpeaks[i] - window_size >= 0 and rpeaks[i] + window_size < len(signal_segment):
                    beat = signal_segment[rpeaks[i] - window_size:rpeaks[i] + window_size]
                    if len(beat) == 2 * window_size:
                        all_beats.append(beat)


            if len(all_beats) >= 5:
                all_beats_array = np.array(all_beats)

                median_template = np.median(all_beats_array, axis=0)
                templates = [median_template]
        def compare_with_dtw(beat, templates):
            min_distance = float('inf')
            for template in templates:
                if len(template) == len(beat):
                    try:
                        # 确保输入是一维数组
                        beat_1d = np.ravel(beat)
                        template_1d = np.ravel(template)
                        distance, _ = fastdtw(beat_1d, template_1d, dist=euclidean)
                        distance = distance / len(beat)  # 归一化距离
                        min_distance = min(min_distance, distance)
                    except Exception:
                        # 使用相关性分析作为备选方法，不输出警告
                        try:
                            corr = np.corrcoef(beat, template)[0, 1]
                            min_distance = min(min_distance, 1.0 - corr)
                        except Exception:
                            pass
            return min_distance

        # --- 新临床诊断逻辑：构建P波模板 ---
        normal_p_waves = []
        normal_p_pr_intervals = []
        normal_qrs_templates_for_pac = [] # 用于PAC QRS形态对比的模板

        for j in range(len(rpeaks)):
             # 条件：非早搏、前后RR间期稳定 -> 收紧标准以"纯化"模板
            if j > 0 and j < len(rr_intervals) -1 and \
               (0.95 * median_rr < rr_intervals[j-1] < 1.05 * median_rr) and \
               (0.95 * median_rr < rr_intervals[j] < 1.05 * median_rr):

                # 寻找该稳定心搏对应的P波
                p_found_for_beat = False
                for p_pos in p_positions:
                    pr_distance_samples = rpeaks[j] - p_pos
                    if 0.08 * sampling_rate < pr_distance_samples < 0.22 * sampling_rate:
                        p_start = max(0, p_pos - int(0.06 * sampling_rate))
                        p_end = min(len(signal_segment), p_pos + int(0.06 * sampling_rate))
                        p_wave_segment = signal_segment[p_start:p_end]

                        # 确保P波模板长度一致
                        if len(p_wave_segment) == int(0.12 * sampling_rate):
                            normal_p_waves.append(p_wave_segment)
                            normal_p_pr_intervals.append(pr_distance_samples / sampling_rate)
                            p_found_for_beat = True
                            break # 每个心搏只找一个P波

                # 如果找到了P波，则将对应的QRS也作为模板
                if p_found_for_beat:
                    if rpeaks[j] - window_size >= 0 and rpeaks[j] + window_size < len(signal_segment):
                        qrs_seg = signal_segment[rpeaks[j] - window_size : rpeaks[j] + window_size]
                        if len(qrs_seg) == 2 * window_size:
                            normal_qrs_templates_for_pac.append(qrs_seg)

        # --- 新逻辑：计算P波模板的内部一致性，用于动态阈值 ---
        avg_normal_p_corr = 1.0
        if len(normal_p_waves) >= 3:
            p_template_corrs = []
            for k in range(len(normal_p_waves)):
                for l in range(k + 1, len(normal_p_waves)):
                    try:
                        corr = np.corrcoef(normal_p_waves[k], normal_p_waves[l])[0, 1]
                        p_template_corrs.append(corr)
                    except:
                        continue
            if p_template_corrs:
                avg_normal_p_corr = np.nanmean(p_template_corrs)

        # 定义动态形态学阈值
        dynamic_morph_threshold = max(0.5, min(0.7, avg_normal_p_corr * 0.85))
        Logger().info(f"P波模板内部平均相关性: {avg_normal_p_corr:.2f}, 动态P'波识别阈值: {dynamic_morph_threshold:.2f}")

        # 如果模板不足，记录警告
        if len(normal_p_waves) < 3:
            Logger().warning(f"正常P波模板不足 ({len(normal_p_waves)}个)，PAC诊断准确性可能受影响")



        # 基于P波模板质量与数量的自适应峰值阈值倍率k（不改变接口，内部自适应）
        dynamic_k = 2.5
        if len(normal_p_waves) < 3 or avg_normal_p_corr < 0.60:
            dynamic_k = 2.0  # 质量或数量不足，放宽阈值，利于I导
        elif avg_normal_p_corr > 0.85 and len(normal_p_waves) >= 5:
            dynamic_k = 2.7  # 模板质量很高，略提高以抑制伪峰

        for i in range(1, len(rr_intervals) - 1):
            # --- 阶段一：是否为"突然"的早搏？（局部自适应） ---
            loc_med_rr = _local_median(rr_intervals, i, w=5)
            is_premature = rr_intervals[i] < 0.85 * loc_med_rr
            is_sudden = rr_intervals[i] < 0.90 * rr_intervals[i-1]  # 保留突发性判断

            if not is_premature or not is_sudden:
                continue

            current_qrs = signal_segment[rpeaks[i] - window_size:rpeaks[i] + window_size]
            if len(current_qrs) != 2 * window_size:
                continue

            # --- 阶段二：P'波检测 (PAC诊断的核心) ---
            found_credible_p_prime = False
            p_prime_morphology_corr = 1.0 # P'波与正常P波的形态相关性

            if normal_p_waves:
                # 在QRS前寻找P'波
                search_start = rpeaks[i] - int(0.25 * sampling_rate) # 扩大搜索窗口
                search_end = rpeaks[i] - int(0.06 * sampling_rate)

                if search_start < 0:
                    continue

                # 在P'波的预期窗口内寻找最可能的候选波峰
                p_search_window = signal_segment[search_start:search_end]
                # 使用自适应高度阈值寻找候选P'波，更鲁棒
                candidate_p_peaks, _ = find_peaks(
                    np.abs(p_search_window),
                    height=_adaptive_peak_height(p_search_window, k=dynamic_k),
                    distance=int(0.04*sampling_rate)
                )

                if len(candidate_p_peaks) > 0:
                     # 选取最接近QRS波的那个作为P'波候选
                    p_prime_pos_relative = candidate_p_peaks[-1]
                    p_prime_pos_absolute = search_start + p_prime_pos_relative

                    # 提取P'波形态
                    p_prime_start = max(0, p_prime_pos_absolute - int(0.06 * sampling_rate))
                    p_prime_end = min(len(signal_segment), p_prime_pos_absolute + int(0.06 * sampling_rate))
                    p_prime_segment = signal_segment[p_prime_start:p_prime_end]

                    # 形态对比
                    if len(p_prime_segment) == int(0.12 * sampling_rate):
                        correlations = []
                        # 与每一个正常P波模板对比
                        for p_template in normal_p_waves:
                            try:
                                corr = np.corrcoef(p_prime_segment, p_template)[0, 1]
                                correlations.append(corr)
                            except:
                                continue

                        if correlations:
                            p_prime_morphology_corr = np.nanmean(correlations)
                            # P'波的定义：形态与正常P波不同 (使用动态阈值)
                            if p_prime_morphology_corr < dynamic_morph_threshold:
                                found_credible_p_prime = True
                                Logger().info(f"心搏 #{i} 发现可信P'波, 与正常P波相关性={p_prime_morphology_corr:.2f} (低于动态阈值 {dynamic_morph_threshold:.2f})")

            # 如果没有找到可信的P'波，则此心搏不可能是PAC
            if not found_credible_p_prime:
                continue

            # --- 阶段三：对找到P'波的心搏进行确认 (QRS & Compensatory Pause) ---
            pac_confidence_score = 6.0 # 找到P'波的证据更强，提高基础置信分

            # 1. QRS形态分析
            if normal_qrs_templates_for_pac:
                qrs_morph_dist = compare_with_dtw(current_qrs, normal_qrs_templates_for_pac)
                # 形态高度相似，是PAC的强证据
                if qrs_morph_dist < 0.2:
                    pac_confidence_score += 3.0
                elif qrs_morph_dist < 0.4:
                    pac_confidence_score += 1.5
                # 形态差异较大，是PAC的否定证据
                elif qrs_morph_dist > 0.7:
                    pac_confidence_score -= 4.0
                Logger().info(f"心搏 #{i} QRS形态距离={qrs_morph_dist:.2f}, 置信度更新为 {pac_confidence_score:.1f}")


            # 2. QRS宽度分析
            qrs_width_current = -1
            q_idx_curr, s_idx_curr = -1, -1
            if i < len(q_indices_hamilton): q_idx_curr = q_indices_hamilton[i]
            if i < len(s_indices_hamilton): s_idx_curr = s_indices_hamilton[i]
            if q_idx_curr != -1 and s_idx_curr != -1 and s_idx_curr > q_idx_curr:
                qrs_width_current = (s_idx_curr - q_idx_curr) / sampling_rate

            if qrs_width_current > 0:
                # 个体化窄/宽阈值（使用正常QRS宽度参照）
                narrow_thr = min(0.11, normal_qrs_width * 1.25)
                wide_thr = max(0.125, normal_qrs_width * 1.6)
                if qrs_width_current < narrow_thr:
                    pac_confidence_score += 2.0
                elif qrs_width_current > wide_thr:
                    pac_confidence_score -= 3.0
                Logger().info(f"心搏 #{i} QRS宽度={qrs_width_current:.3f}s, 置信度更新为 {pac_confidence_score:.1f}")

            # 3. 代偿间期分析（自适应 z 分数）
            current_rr = rr_intervals[i]
            next_rr = rr_intervals[i+1]
            two_beat_sum = current_rr + next_rr
            loc_med = _local_median(rr_intervals, i, w=5)
            loc_mad = _local_mad(rr_intervals, i, w=5)
            expected_sum = 2 * loc_med
            z_dev = abs(two_beat_sum - expected_sum) / max(1e-6, loc_mad)

            # 不完全代偿：z 较小
            if z_dev < 2.0:
                pac_confidence_score += 2.0
            # 明显完全代偿：z 较大
            elif z_dev > 3.0:
                pac_confidence_score -= 2.0
            Logger().info(f"心搏 #{i} 代偿 z={z_dev:.2f}, 2RR={two_beat_sum:.2f}, exp={expected_sum:.2f}, 置信度={pac_confidence_score:.1f}")

            # --- 阶段四：最终决策 ---
            pac_threshold = 7.5 # 微调置信度阈值

            is_pac = pac_confidence_score >= pac_threshold
            is_borderline_pac = pac_threshold - 2.0 <= pac_confidence_score < pac_threshold

            if is_pac:
                pac_count += 1
                # (beat_index, rpeak_location, pac_score, pvc_score_placeholder)
                pac_beats.append((i, rpeaks[i], pac_confidence_score, 0))
                Logger().info(
                    f"PAC心搏 #{i} 在 {rpeaks[i] / sampling_rate:.2f}秒, 最终置信度={pac_confidence_score:.1f} (阈值>{pac_threshold})")
            elif is_borderline_pac:
                Logger().info(
                    f"边缘PAC心搏 #{i} 在 {rpeaks[i] / sampling_rate:.2f}秒, 最终置信度={pac_confidence_score:.1f}")
                borderline_pac_beats.append((i, rpeaks[i], pac_confidence_score, 0))

        # --- 连发PAC和特殊模式检测 ---
        has_coupled_pac = False  # 成对PAC
        has_bigeminy = False    # 二联律
        has_trigeminy = False   # 三联律
        has_run_pac = False     # 短阵PAC

        if len(pac_beats) >= 2:
            pac_indices = [b[0] for b in pac_beats]

            # 检测连续PAC
            consecutive_count = 0
            consecutive_runs = []
            current_run = []

            for i in range(len(pac_indices)):
                if i == 0 or pac_indices[i] == pac_indices[i - 1] + 1:
                    consecutive_count += 1
                    current_run.append(pac_indices[i])
                else:
                    if consecutive_count >= 2:
                        consecutive_runs.append(current_run.copy())
                    current_run = [pac_indices[i]]
                    consecutive_count = 1

            if consecutive_count >= 2:
                consecutive_runs.append(current_run)

            if consecutive_runs:
                has_coupled_pac = True
                if any(len(run) >= 3 for run in consecutive_runs):
                    has_run_pac = True
                    Logger().info(f"检测到连发PAC: {len(consecutive_runs)}组，最长{max([len(run) for run in consecutive_runs])}个连续")

            # 检测二联律（一正常一早搏）
            bigeminy_count = 0
            for i in range(len(rpeaks) - 2):
                if i in pac_indices and i + 2 in pac_indices and i + 1 not in pac_indices:
                    bigeminy_count += 1

            if bigeminy_count >= 2:
                has_bigeminy = True
                Logger().info(f"检测到房性二联律，重复{bigeminy_count}次")

            # 检测三联律（两正常一早搏）
            trigeminy_count = 0
            for i in range(len(rpeaks) - 3):
                if i in pac_indices and i + 3 in pac_indices and \
                   i + 1 not in pac_indices and i + 2 not in pac_indices:
                    trigeminy_count += 1

            if trigeminy_count >= 2:
                has_trigeminy = True
                Logger().info(f"检测到房性三联律，重复{trigeminy_count}次")

        special_patterns_detected = has_coupled_pac or has_bigeminy or has_trigeminy or has_run_pac

        if special_patterns_detected:
            special_pattern_info = []
            if has_coupled_pac: special_pattern_info.append("成对PAC")
            if has_bigeminy: special_pattern_info.append("房性二联律")
            if has_trigeminy: special_pattern_info.append("房性三联律")
            if has_run_pac: special_pattern_info.append("短阵PAC")

            Logger().info(f"检测到特殊PAC模式: {', '.join(special_pattern_info)}")

            borderline_beats = []
            for i, r_pos, score, _ in pac_beats:
                if score < pac_threshold:
                    borderline_beats.append((i, r_pos))

            if special_patterns_detected and borderline_beats:
                reduced_threshold = pac_threshold - 0.8

                for i in range(1, len(rr_intervals) - 1):
                    if i not in [b[0] for b in pac_beats]:
                        beat_score = 0
                        if beat_score >= reduced_threshold:
                            pac_count += 1
                            Logger().info(f"心搏 #{i} 在特殊模式宽松阈值下被确认为PAC")

            if pac_count >= 1:
                return True

        has_definite_pac = pac_count > 0
        has_borderline_pac = len(borderline_pac_beats) > 0
        has_pattern_evidence = has_coupled_pac or has_bigeminy or has_trigeminy

        if not has_definite_pac and has_borderline_pac:
            strong_features_count = 0
            for _, _, score, _ in borderline_pac_beats:
                if score > pac_threshold - 0.5:
                    strong_features_count += 1

            if strong_features_count >= 2:
                Logger().info(f"多个边缘PAC心搏检测，结合判定为PAC")
                return True

            if has_pattern_evidence and strong_features_count >= 1:
                Logger().info(f"边缘PAC心搏+特殊模式证据，结合判定为PAC")
                return True

        if has_definite_pac or (has_pattern_evidence and has_borderline_pac):
            return True
        else:
            return False
    except Exception:
        Logger().error(f'PAC诊断异常：\n{traceback.format_exc()}')
        return False
