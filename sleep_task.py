# predict1.py
import torch
import numpy as np
from scipy.signal import resample_poly
from model import EnhancedSleepModel
from preprocess1 import read_signal1, extract_ecg, extract_resp
from preprocess import _butter_band
from scipy.signal import butter, filtfilt
LABELS = ['W', 'N1-N3', 'R']
#LABELS = ['W', 'N1','N2','N3', 'R']

def smooth_signal(sig, fs, low=0.5, high=35, notch=True):
    sig = _butter_band(sig, fs, low, high, order=6)
    if notch and fs >= 100:          # 只在 ≥100 Hz 时才陷波
        f0 = 50
        b, a = butter(2, [f0-0.5, f0+0.5], btype='bandstop', fs=fs)
        sig = filtfilt(b, a, sig)
    return sig


# ----------- 降采样 -----------
def downsample(sig, orig_fs, target_fs):
    """把信号从 orig_fs 降采样到 target_fs（整数倍关系用 resample_poly 最稳）"""
    if abs(orig_fs - target_fs) < 1e-3:
        return sig
    q = int(round(orig_fs / target_fs))
    return resample_poly(sig, up=1, down=q)

# ------------------------------------

def predict(ecg_path, resp_path, weights_path='finetuned.pth'):
    # 1. 从原 checkpoint 读超参与标准化参数
    base_ckpt = torch.load('best_model.pth', map_location='cpu')
    ecg_feat_size  = base_ckpt['ecg_feat_size']

    resp_feat_size = base_ckpt['resp_feat_size']
    ecg_mean  = base_ckpt['ecg_mean']
    ecg_std   = base_ckpt['ecg_std']
    resp_mean = base_ckpt['resp_mean']
    resp_std  = base_ckpt['resp_std']

    # 2. 建模型
    model = EnhancedSleepModel(
        ecg_feat_size=ecg_feat_size,
        resp_feat_size=resp_feat_size,
        num_classes=3
    )

    # 3. 加载微调后的权重
    model.load_state_dict(torch.load(weights_path, map_location='cpu'))
    model.eval()


    ecg_fs = 200
    resp_fs = 12.5
    window_sec = 30
    ecg_len  = int(ecg_fs * window_sec)
    resp_len = int(resp_fs * window_sec)
    ecg = read_signal1(ecg_path, fs=250, stype='ecg')
    resp = read_signal1(resp_path, fs=250, stype='resp')
    num_windows = min(len(ecg) // ecg_len, len(resp) // resp_len)

    for i in range(num_windows):
        ecg_seg  = ecg[i*ecg_len:(i+1)*ecg_len]
        resp_seg = resp[i*resp_len:(i+1)*resp_len]

        ecg_feat  = np.array(extract_ecg(ecg_seg,  fs=ecg_fs)).reshape(1, -1)
        resp_feat = np.array(extract_resp(resp_seg, fs=resp_fs)).reshape(1, -1)

        ecg_feat  = (ecg_feat  - ecg_mean)  / ecg_std
        resp_feat = (resp_feat - resp_mean) / resp_std

        with torch.no_grad():
            output = model(
                torch.tensor(ecg_feat,  dtype=torch.float32),
                torch.tensor(resp_feat, dtype=torch.float32)
            )
            pred_class = torch.argmax(output, dim=1).item()
        print(f"窗口 {i+1}/{num_windows} 预测结果: {LABELS[pred_class]}")

if __name__ == "__main__":
    predict("ecg1need1quan", "breath1_numbersneed1quan", weights_path="finetuned.pth")