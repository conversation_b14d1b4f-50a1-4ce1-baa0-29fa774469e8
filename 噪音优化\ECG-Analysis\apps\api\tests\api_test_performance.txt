         28462378 function calls (28460944 primitive calls) in 279.212 seconds

   Ordered by: internal time

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
     1212  230.228    0.190  230.228    0.190 {method 'recv_into' of '_socket.socket' objects}
      300   32.181    0.107   32.181    0.107 {built-in method time.sleep}
        2    9.937    4.969    9.937    4.969 {built-in method builtins.input}
      300    1.562    0.005  267.965    0.893 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:639(ecg_analysis)
  3566011    0.762    0.000    1.386    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:150(_isna)
        1    0.558    0.558  268.919  268.919 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:967(process_single_file)
      300    0.557    0.002    0.557    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\encoder.py:306(iterencode)
  3566011    0.533    0.000    1.920    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:67(isna)
  1783000    0.443    0.000    1.491    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:288(notna)
  3566006    0.442    0.000    0.442    0.000 {built-in method pandas._libs.missing.checknull}
  5349023    0.290    0.000    0.290    0.000 {pandas._libs.lib.is_scalar}
  3581263    0.199    0.000    0.199    0.000 {method 'append' of 'list' objects}
1960834/1960231    0.130    0.000    0.139    0.000 {built-in method builtins.isinstance}
  1787516    0.113    0.000    0.113    0.000 {method 'strip' of 'str' objects}
  1783000    0.095    0.000    0.095    0.000 {built-in method builtins.abs}
     5726    0.069    0.000    0.069    0.000 {method 'split' of 'str' objects}
      633    0.042    0.000    0.042    0.000 {built-in method nt.stat}
    98455    0.039    0.000    0.092    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:670(__getitem__)
      602    0.038    0.000    0.038    0.000 {method 'sendall' of '_socket.socket' objects}
   104444    0.037    0.000    0.144    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:742(__iter__)
        1    0.033    0.033    0.037    0.037 {method 'read' of '_io.TextIOWrapper' objects}
        1    0.028    0.028    0.028    0.028 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\decoder.py:343(raw_decode)
      341    0.028    0.000    0.028    0.000 {built-in method builtins.min}
    98455    0.027    0.000    0.053    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:740(encodekey)
      602    0.025    0.000    0.165    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2494(getproxies_environment)
      903    0.024    0.000    0.024    0.000 {built-in method winreg.QueryValueEx}
     3639    0.022    0.000    0.065    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:288(__init__)
     3639    0.018    0.000    0.018    0.000 {method 'write' of '_io.TextIOWrapper' objects}
     4259    0.018    0.000    0.018    0.000 {built-in method builtins.max}
    96320    0.015    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:693(__iter__)
   114461    0.014    0.000    0.014    0.000 {method 'lower' of 'str' objects}
      903    0.014    0.000    0.020    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:227(_encode_invalid_chars)
    98455    0.013    0.000    0.018    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:734(check_str)
      602    0.012    0.000    0.012    0.000 {built-in method winreg.OpenKey}
     3942    0.012    0.000    0.023    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:180(split)
     3639    0.011    0.000    0.029    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1514(findCaller)
      300    0.010    0.000  267.976    0.893 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:620(profile_api_call)
    99966    0.009    0.000    0.009    0.000 {method 'upper' of 'str' objects}
     3011    0.007    0.000    0.019    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:366(urlparse)
      301    0.007    0.000  230.345    0.765 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:481(getresponse)
     9044    0.007    0.000    0.007    0.000 {method 'match' of 're.Pattern' objects}
     3944    0.006    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:124(splitdrive)
      301    0.006    0.000    0.006    0.000 {built-in method builtins.sum}
      602    0.006    0.000    0.033    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:369(parse_url)
     7278    0.006    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:44(normcase)
     3639    0.006    0.000    0.170    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1565(_log)
      496    0.006    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:204(iterencode)
     2407    0.006    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1208(putheader)
      301    0.006    0.000  230.474    0.766 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:594(urlopen)
      300    0.006    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:33(select_wait_for_socket)
        1    0.006    0.006    0.006    0.006 {pandas._libs.writers.write_csv_rows}
      301    0.005    0.000  230.596    0.766 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:673(send)
    25469    0.005    0.000    0.005    0.000 {built-in method builtins.hasattr}
     2107    0.005    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:824(update)
      301    0.005    0.000    0.078    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:365(request)
     3639    0.005    0.000    0.050    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1073(emit)
      301    0.005    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\decoder.py:392(raw_decode)
      301    0.005    0.000  231.554    0.769 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:500(request)
      301    0.005    0.000  230.545    0.766 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:613(send)
     3392    0.005    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2084(debug)
41685/41023    0.005    0.000    0.005    0.000 {built-in method builtins.len}
     3642    0.005    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\genericpath.py:121(_splitext)
      301    0.005    0.000    0.063    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:204(parse_headers)
      301    0.005    0.000    0.723    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:457(prepare_request)
     3312    0.005    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:417(urlsplit)
      301    0.005    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1052(putrequest)
     3639    0.005    0.000    0.058    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:941(handle)
      301    0.004    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:301(makefile)
     3010    0.004    0.000  230.234    0.076 {method 'readline' of '_io.BufferedReader' objects}
      301    0.004    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:95(_default_key_normalizer)
      602    0.004    0.000    0.027    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:218(_parsegen)
     3639    0.004    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:655(format)
      301    0.004    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:471(_parse_headers)
      301    0.004    0.000  230.430    0.766 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:379(_make_request)
        3    0.004    0.001    0.004    0.001 {built-in method _codecs.utf_8_decode}
      903    0.004    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:81(RLock)
     6925    0.004    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:110(_coerce_args)
     3323    0.004    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:657(get)
     8425    0.004    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:46(__setitem__)
     7239    0.004    0.000    0.004    0.000 {built-in method _abc._abc_instancecheck}
     2107    0.004    0.000    0.023    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:61(merge_setting)
     3247    0.004    0.000    0.160    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1436(info)
      301    0.004    0.000    0.054    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:204(get_netrc_auth)
    18969    0.004    0.000    0.004    0.000 {method 'get' of 'dict' objects}
     3639    0.004    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1062(flush)
     3639    0.004    0.000    0.062    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1645(callHandlers)
      301    0.004    0.000  230.227    0.765 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:267(_read_status)
     3247    0.004    0.000    0.164    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2074(info)
      301    0.004    0.000  230.295    0.765 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:300(begin)
     3639    0.003    0.000    0.069    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1550(makeRecord)
     8124    0.003    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:51(__getitem__)
     3642    0.003    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:203(splitext)
      301    0.003    0.000    0.013    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:337(extend)
      301    0.003    0.000    0.032    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:409(prepare_url)
     2108    0.003    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:345(to_key_val_list)
      602    0.003    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:521(cookiejar_from_dict)
11749/11748    0.003    0.000    0.003    0.000 {method 'encode' of 'str' objects}
     3639    0.003    0.000    0.066    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1591(handle)
      301    0.003    0.000    0.034    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:359(build_response)
     1212    0.003    0.000  230.232    0.190 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:655(readinto)
     2408    0.003    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:462(get)
      301    0.003    0.000    0.568    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:494(prepare_body)
     3913    0.003    0.000    0.003    0.000 {method 'search' of 're.Pattern' objects}
      903    0.003    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1258(__init__)
      496    0.003    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\__init__.py:183(dumps)
     7333    0.003    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1689(isEnabledFor)
      301    0.003    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:476(readinto)
     2407    0.003    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:351(putheader)
      602    0.003    0.000    0.025    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:858(_raw_read)
     7278    0.003    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:898(acquire)
     2709    0.003    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:155(hostname)
      301    0.002    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:658(__init__)
      301    0.002    0.000    0.045    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:59(parsestr)
      301    0.002    0.000    0.127    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:765(should_bypass_proxies)
      301    0.002    0.000    0.116    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:114(proxy_bypass)
     1505    0.002    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:40(__init__)
      605    0.002    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:289(expanduser)
     3639    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:435(_format)
    23440    0.002    0.000    0.002    0.000 {method 'decode' of 'bytes' objects}
      301    0.002    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:139(__init__)
    10938    0.002    0.000    0.002    0.000 {method 'rfind' of 'str' objects}
     3010    0.002    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:195(_hostinfo)
     2408    0.002    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:293(header_source_parse)
      301    0.002    0.000    0.227    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:750(merge_environment_settings)
      301    0.002    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:483(prepare_headers)
      301    0.002    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:573(__init__)
      301    0.002    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:315(__init__)
    11238    0.002    0.000    0.002    0.000 {method 'replace' of 'str' objects}
     3694    0.002    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1424(debug)
      301    0.002    0.000    0.032    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:910(read)
    10531    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:58(<genexpr>)
     3612    0.002    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:259(__getitem__)
      602    0.002    0.000    0.002    0.000 {method 'settimeout' of '_socket.socket' objects}
     7239    0.002    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\abc.py:96(__instancecheck__)
     3941    0.002    0.000    0.025    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:214(basename)
      702    0.002    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:341(notify)
     2408    0.002    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:302(add)
      301    0.002    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1353(add_cookie_header)
     3639    0.002    0.000    0.017    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:918(format)
     1204    0.002    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:564(get_content_type)
     3311    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:78(readline)
     8488    0.002    0.000    0.002    0.000 {method 'acquire' of '_thread.RLock' objects}
     7859    0.002    0.000    0.002    0.000 {method 'startswith' of 'str' objects}
     3639    0.002    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:160(<lambda>)
      496    0.002    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:182(encode)
     3639    0.002    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:427(usesTime)
      903    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:877(__init__)
     3639    0.002    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:633(usesTime)
      301    0.002    0.000  230.305    0.765 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1300(getresponse)
      301    0.002    0.000    0.633    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:351(prepare)
    19422    0.002    0.000    0.002    0.000 {built-in method nt.fspath}
      602    0.002    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1596(make_cookies)
      903    0.002    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:35(__init__)
      602    0.002    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:263(_remove_path_dot_segments)
     7278    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:905(release)
      602    0.002    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:444(read)
     1505    0.002    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1219(vals_sorted_by_key)
      301    0.002    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:237(__init__)
      301    0.002    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:153(get)
      401    0.002    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:121(put)
     3913    0.002    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:281(_sanitize_header)
      602    0.002    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1677(extract_cookies)
      602    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:100(__new__)
      301    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:53(__init__)
      602    0.002    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:124(extract_cookies_to_jar)
     6667    0.002    0.000    0.002    0.000 {method 'find' of 'str' objects}
     5249    0.002    0.000    0.002    0.000 {built-in method time.time}
      301    0.002    0.000    0.043    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:41(parse)
     4834    0.002    0.000    0.002    0.000 {built-in method __new__ of type object at 0x00007FFAAEDBB810}
     3639    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:119(getLevelName)
     7278    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:796(filter)
      904    0.002    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:109(__init__)
     3639    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:1306(current_thread)
     1204    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:497(get_all)
     6210    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\decoder.py:52(bounded_int)
     3639    0.002    0.000    0.002    0.000 {method 'flush' of '_io.TextIOWrapper' objects}
    22858    0.001    0.000    0.001    0.000 {built-in method builtins.ord}
      301    0.001    0.000    0.029    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:76(proxy_bypass_registry)
      301    0.001    0.000    0.022    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:446(get_connection_with_tls_context)
      602    0.001    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:838(select_proxy)
     3610    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1045(_validate_header_part)
      300    0.001    0.000    0.559    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\encoder.py:277(encode)
     2107    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:664(__contains__)
      903    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:82(__init__)
     6974    0.001    0.000    0.001    0.000 {method 'rstrip' of 'str' objects}
     2408    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:676(items)
      301    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:40(assert_header_parsing)
     3227    0.001    0.000    0.001    0.000 {built-in method builtins.getattr}
      302    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:868(__new__)
     3639    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:364(getMessage)
      602    0.001    0.000    0.001    0.000 {method 'readlines' of '_io._IOBase' objects}
      301    0.001    0.000  231.555    0.769 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:626(post)
      903    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:238(helper)
      301    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:258(__init__)
      603    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:255(inner)
      301    0.001    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1018(get_auth_from_url)
     2352    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:333(_iterencode_dict)
      301    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:255(get)
     3310    0.001    0.000    0.035    0.000 {method 'join' of 'bytes' objects}
     3639    0.001    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:639(formatMessage)
      301    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:101(push)
      301    0.001    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:242(__init__)
      301    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:90(_urllib3_request_context)
     9947    0.001    0.000    0.001    0.000 {method 'partition' of 'str' objects}
     3311    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:128(__next__)
     2407    0.001    0.000    0.001    0.000 {method 'fullmatch' of 're.Pattern' objects}
     2407    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:400(<genexpr>)
     1505    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:57(__iter__)
     4544    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:34(_get_bothseps)
      301    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\request.py:189(body_to_chunks)
      301    0.001    0.000    0.001    0.000 {method 'Close' of 'PyHKEY' objects}
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:643(__init__)
     1507    0.001    0.000    0.001    0.000 {built-in method builtins.sorted}
      301    0.001    0.000    0.013    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:258(_get_conn)
     3639    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:438(format)
     4214    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\utils.py:51(_has_surrogates)
      301    0.001    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:305(connection_from_context)
     2712    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:130(_validate_timeout)
     2710    0.001    0.000    0.002    0.000 {built-in method builtins.any}
      302    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:106(_encode_params)
      903    0.001    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:117(__exit__)
     1204    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:29(_splitparam)
      903    0.001    0.000    0.036    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:890(content)
     3639    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:1031(name)
     1505    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1223(deepvalues)
      603    0.001    0.000    0.041    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\genericpath.py:16(exists)
      301    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:679(_init_length)
      301    0.001    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:140(get_cookie_header)
     8047    0.001    0.000    0.001    0.000 {method 'join' of 'str' objects}
      301    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:572(prepare_content_length)
      301    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:539(get_encoding_from_headers)
      301    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:334(__init__)
      301    0.001    0.000    0.004    0.000 {method 'readinto' of '_io.BufferedReader' objects}
     3913    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:311(header_fetch_parse)
      301    0.001    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:331(putrequest)
     1805    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1034(check_header_validity)
     1505    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1750(__iter__)
     4814    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:353(<genexpr>)
     8488    0.001    0.000    0.001    0.000 {method 'release' of '_thread.RLock' objects}
      602    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:120(__init__)
      301    0.001    0.000    0.220    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:826(get_environ_proxies)
  686/616    0.001    0.000    0.001    0.000 {built-in method _abc._abc_subclasscheck}
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:514(_parse_content_type_header)
     5228    0.001    0.000    0.001    0.000 {method 'items' of 'dict' objects}
      301    0.001    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2662(getproxies_registry)
      301    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:330(connection_from_pool_key)
      301    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:802(__getitem__)
      602    0.001    0.000    0.039    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:939(send)
     7286    0.001    0.000    0.001    0.000 {built-in method _thread.get_ident}
      602    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:542(merge_cookies)
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:732(close)
     3949    0.001    0.000    0.001    0.000 {method 'rpartition' of 'str' objects}
      301    0.001    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:546(request_url)
      301    0.001    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:947(json)
      602    0.001    0.000    0.033    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:1038(stream)
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:781(get_adapter)
      702    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:246(__enter__)
      301    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:459(<listcomp>)
      300    0.001    0.000    0.560    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\__init__.py:276(dumps)
     1204    0.001    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:735(_error_catcher)
      903    0.001    0.000    0.001    0.000 {method 'subn' of 're.Pattern' objects}
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:85(path_url)
      301    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:474(urlunparse)
     2708    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:977(_output)
      602    0.001    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:801(_fp_read)
     3639    0.001    0.000    0.001    0.000 {built-in method nt.getpid}
      301    0.001    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\decoder.py:379(decode)
     3639    0.001    0.000    0.001    0.000 {built-in method sys._getframe}
     3310    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\util.py:19(to_str)
      905    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:720(__hash__)
     2408    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:479(set_raw)
      903    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:108(__enter__)
      301    0.001    0.000    0.025    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:998(_send_output)
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:909(text)
      104    0.001    0.000    0.001    0.000 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:878(get_disease_name)
      301    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:207(register_hook)
     2709    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:291(__iter__)
      602    0.001    0.000    0.028    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:178(_call_parse)
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:213(get_payload)
      305    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:799(quote)
      301    0.001    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:276(connection_from_host)
     2352    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:413(_iterencode)
     1807    0.001    0.000    0.007    0.000 {built-in method builtins.next}
        1    0.001    0.001    0.066    0.066 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\__init__.py:274(load)
      301    0.001    0.000    0.092    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2707(getproxies)
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:135(super_len)
        2    0.001    0.000    0.001    0.000 {built-in method io.open}
      301    0.001    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:296(_put_conn)
      602    0.001    0.000    0.034    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:816(generate)
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:485(urlunsplit)
      300    0.001    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:15(is_connection_dropped)
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:624(unquote)
      301    0.001    0.000    0.002    0.000 {method 'close' of '_io.BufferedReader' objects}
     6925    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:99(_noop)
      496    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:104(__init__)
      602    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:348(_get_timeout)
      302    0.001    0.000    0.001    0.000 {method 'pop' of 'collections.OrderedDict' objects}
      602    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:15(default_hooks)
      305    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:870(quote_from_bytes)
     2408    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:698(__init__)
      602    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:188(clone)
      301    0.001    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:184(close)
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:95(__getitem__)
     3354    0.001    0.000    0.001    0.000 {method 'setdefault' of 'dict' objects}
     1212    0.001    0.000    0.001    0.000 {method '_checkReadable' of '_io._IOBase' objects}
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:244(__init__)
      301    0.001    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\__init__.py:459(loads)
      301    0.001    0.000    0.031    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:173(feed)
      301    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:159(resolve_redirects)
      602    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:534(<listcomp>)
     1513    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:694(readable)
      702    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:249(__exit__)
      301    0.001    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:610(prepare_cookies)
      301    0.001    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:588(prepare_auth)
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:304(cert_verify)
      603    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:771(__subclasscheck__)
     1542    0.001    0.000    0.001    0.000 {method 'endswith' of 'str' objects}
      608    0.001    0.000    0.001    0.000 {method 'extend' of 'list' objects}
      301    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:774(get_proxy)
      604    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:303(_normalize_host)
      903    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:84(<listcomp>)
     2106    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\_internal_utils.py:25(to_native_string)
      702    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:261(_is_owned)
      196    0.001    0.000    0.001    0.000 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:1581(<dictcomp>)
      603    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:768(__instancecheck__)
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1731(clear_expired_cookies)
     2718    0.001    0.000    0.001    0.000 {method 'lstrip' of 'str' objects}
      602    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:358(update)
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:799(iter_content)
      903    0.001    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:588(get_content_maintype)
      301    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:446(_init_decoder)
     1805    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:434(isclosed)
      301    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:660(requote_uri)
      300    0.001    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:113(wait_for_read)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:1079(closed)
      602    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:635(release_conn)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:374(_merge_pool_kwargs)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:636(unquote_unreserved)
      602    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:251(put)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:166(port)
      602    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:403(retries)
      302    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:892(urlencode)
        3    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2105(_merge_blocks)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:379(_is_method_retryable)
      301    0.000    0.000    0.025    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1236(endheaders)
      498    0.000    0.000    0.000    0.000 {method 'update' of 'dict' objects}
        1    0.000    0.000    0.000    0.000 {method 'connect' of '_socket.socket' objects}
      301    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:630(prepare_hooks)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:70(close)
      602    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:122(pushlines)
      903    0.000    0.000    0.000    0.000 {method 'count' of 'bytes' objects}
      300    0.000    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:299(is_connected)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:197(_new_message)
      301    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:76(copy)
      301    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:351(_encode_target)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:411(close)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:393(prepare_method)
      602    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:16(<dictcomp>)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:183(_userinfo)
      903    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:60(__len__)
      903    0.000    0.000    0.000    0.000 {method 'decode' of 'bytearray' objects}
      196    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:259(_make_iterencode)
      602    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:9(is_fp_closed)
      702    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:245(_qsize)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:245(read_timeout)
      301    0.000    0.000    0.000    0.000 {method 'sort' of 'list' objects}
      902    0.000    0.000    0.000    0.000 {method 'copy' of 'dict' objects}
      301    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:396(build_connection_pool_key_attributes)
      903    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:211(<genexpr>)
     1204    0.000    0.000    0.000    0.000 {method 'groups' of 're.Match' objects}
     1212    0.000    0.000    0.000    0.000 {method '_checkClosed' of '_io._IOBase' objects}
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:769(is_redirect)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1294(_cookie_attrs)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:107(get_redirect_target)
      105    0.000    0.000    0.356    0.003 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:525(get_token)
      304    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:389(parent)
  686/616    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\abc.py:100(__subclasscheck__)
       19    0.000    0.000    0.000    0.000 {pandas._libs.lib.maybe_convert_objects}
      105    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:91(_path_join)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:202(start_connect)
      196    0.000    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2059(warning)
     1204    0.000    0.000    0.000    0.000 {method 'count' of 'str' objects}
      903    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:44(_debug)
      196    0.000    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2041(error)
     1806    0.000    0.000    0.000    0.000 {method 'values' of 'collections.OrderedDict' objects}
      350    0.000    0.000    0.000    0.000 {method 'clear' of 'dict' objects}
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:147(username)
      602    0.000    0.000    0.001    0.000 {function SocketIO.close at 0x00000264EBFA5DC0}
      602    0.000    0.000    0.000    0.000 {method 'read' of '_io.StringIO' objects}
     2454    0.000    0.000    0.000    0.000 {method 'pop' of 'dict' objects}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:210(_pop_message)
      725    0.000    0.000    0.001    0.000 {built-in method builtins.issubclass}
     2408    0.000    0.000    0.000    0.000 {method 'keys' of 'dict' objects}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:486(_decref_socketios)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:387(is_retry)
      412    0.000    0.000    0.000    0.000 {built-in method builtins.all}
      923    0.000    0.000    0.000    0.000 {built-in method builtins.hash}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1183(_validate_method)
      301    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:406(_close_conn)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:706(<setcomp>)
      301    0.000    0.000    0.000    0.000 {method 'tobytes' of 'memoryview' objects}
      602    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:181(is_multipart)
      301    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:451(items)
      301    0.000    0.000    0.000    0.000 {method 'write' of '_io.StringIO' objects}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:91(merge_hooks)
      196    0.000    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1465(error)
      196    0.000    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1448(warning)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\_internal_utils.py:38(unicode_is_ascii)
     3311    0.000    0.000    0.000    0.000 {method 'popleft' of 'collections.deque' objects}
      602    0.000    0.000    0.000    0.000 {method 'extend' of 'collections.deque' objects}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:303(set_payload)
      702    0.000    0.000    0.000    0.000 {method 'acquire' of '_thread.lock' objects}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:423(flush)
      301    0.000    0.000    0.000    0.000 <string>:1(__new__)
      301    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:724(<listcomp>)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1287(_cookies_for_request)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1192(_validate_path)
      602    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:701(__len__)
      305    0.000    0.000    0.000    0.000 {method 'rstrip' of 'bytes' objects}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:17(__init__)
        1    0.000    0.000    0.000    0.000 {pandas._libs.missing.isnaobj2d}
      602    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:110(__init__)
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:1034(get_data)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:22(dispatch_hook)
      301    0.000    0.000    0.000    0.000 {built-in method _codecs.utf_8_encode}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:251(_get)
      301    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:377(_check_close)
      401    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:248(_put)
      303    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:164(host)
      603    0.000    0.000    0.000    0.000 {method 'pop' of 'list' objects}
      104    0.000    0.000    0.000    0.000 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:924(get_multi_label_disease_name)
      602    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:343(<genexpr>)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\request.py:134(set_file_position)
     1204    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:248(__len__)
      602    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:743(set_environ)
      702    0.000    0.000    0.000    0.000 {method '__enter__' of '_thread.lock' objects}
     1204    0.000    0.000    0.000    0.000 {method 'seek' of '_io.StringIO' objects}
      602    0.000    0.000    0.000    0.000 {method 'write' of '_io.BytesIO' objects}
      603    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:126(resolve_default_timeout)
        3    0.000    0.000    0.000    0.000 {built-in method io.open_code}
     1204    0.000    0.000    0.000    0.000 {method 'items' of 'collections.OrderedDict' objects}
        3    0.000    0.000    0.000    0.000 {built-in method marshal.loads}
        1    0.000    0.000    0.000    0.000 {pandas._libs.lib.dicts_to_array}
      602    0.000    0.000    0.000    0.000 {built-in method time.perf_counter}
     1204    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:117(info)
       21    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap_external>:1498(find_spec)
      301    0.000    0.000    0.000    0.000 {method 'getvalue' of '_io.BytesIO' objects}
      392    0.000    0.000    0.000    0.000 {built-in method _json.encode_basestring}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1179(_encode_request)
      104    0.000    0.000    0.000    0.000 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:799(<listcomp>)
      602    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:465(_decode)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:958(<genexpr>)
      602    0.000    0.000    0.000    0.000 {method 'truncate' of '_io.StringIO' objects}
      602    0.000    0.000    0.000    0.000 {method 'update' of 'collections.OrderedDict' objects}
      603    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:227(connect_timeout)
      104    0.000    0.000    0.000    0.000 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:918(<listcomp>)
        3    0.000    0.000    0.000    0.000 {method 'read' of '_io.BufferedReader' objects}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\util.py:7(to_bytes)
    81/77    0.000    0.000    0.000    0.000 {built-in method numpy.array}
        1    0.000    0.000    0.000    0.000 {method 'close' of '_io.TextIOWrapper' objects}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\proxy.py:11(connection_requires_http_tunnel)
      438    0.000    0.000    0.000    0.000 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:916(<genexpr>)
      702    0.000    0.000    0.000    0.000 {method '__exit__' of '_thread.lock' objects}
        7    0.000    0.000    0.000    0.000 {built-in method builtins.__build_class__}
      407    0.000    0.000    0.000    0.000 {method 'values' of 'dict' objects}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:79(<listcomp>)
      602    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:125(__iter__)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:216(<genexpr>)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:31(encode)
      604    0.000    0.000    0.000    0.000 {method 'end' of 're.Match' objects}
      301    0.000    0.000    0.000    0.000 {built-in method time.monotonic}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:87(get_new_headers)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:219(__init__)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:581(iter_slices)
      602    0.000    0.000    0.000    0.000 {method 'append' of 'collections.deque' objects}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:578(add_headers)
      301    0.000    0.000    0.000    0.000 {method 'pop' of 'set' objects}
      196    0.000    0.000    0.000    0.000 {built-in method builtins.id}
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:339(_validate_conn)
      353    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:1149(cast)
        1    0.000    0.000    0.000    0.000 {pandas._libs.lib.fast_unique_multiple_list_gen}
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:493(_parse)
      104    0.000    0.000    0.000    0.000 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:919(<listcomp>)
        1    0.000    0.000    0.000    0.000 {built-in method nt.listdir}
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:470(sanitize_array)
     12/4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:71(_compile)
        2    0.000    0.000    0.000    0.000 {pandas._libs.ops.scalar_compare}
        8    0.000    0.000    0.000    0.000 {built-in method nt.get_terminal_size}
      4/2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:397(__new__)
        4    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap>:890(_find_spec)
      301    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:295(is_closed)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1372(_clear_cache)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2022(_form_blocks)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:177(__init__)
       34    0.000    0.000    0.000    0.000 {built-in method numpy.empty}
      301    0.000    0.000    0.000    0.000 {function HTTPResponse.flush at 0x00000264EC540A60}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:965(<listcomp>)
        2    0.000    0.000    0.000    0.000 {method '_rebuild_blknos_and_blklocs' of 'pandas._libs.internals.BlockManager' objects}
       15    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1579(maybe_cast_to_datetime)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5517(__finalize__)
      105    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:114(<listcomp>)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\re.py:289(_compile)
      152    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\generic.py:43(_check)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1466(maybe_infer_to_datetimelike)
    29/25    0.000    0.000    0.000    0.000 {built-in method numpy.core._multiarray_umath.implement_array_function}
        4    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap_external>:1367(_get_spec)
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:914(get_code)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1337(add_argument)
        6    0.000    0.000    0.000    0.000 {built-in method builtins.print}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2240(is_unique)
        5    0.000    0.000    0.003    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:587(__init__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2153(to_native_types)
        4    0.000    0.000    0.004    0.001 <frozen importlib._bootstrap>:986(_find_and_load)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:157(_get_module_lock)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2074(_stack_arrays)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:239(__init__)
        1    0.000    0.000    0.006    0.006 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:296(_save_body)
        3    0.000    0.000    0.004    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:319(decode)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexers\utils.py:249(maybe_convert_indices)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:1(<module>)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\shutil.py:1312(get_terminal_size)
        1    0.000    0.000    0.011    0.011 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3376(to_csv)
        1    0.000    0.000    0.000    0.000 {built-in method _socket.getaddrinfo}
        2    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis1_float64_float64}
       23    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1330(_path_importer_cache)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numeric.py:268(full)
        2    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:672(_with_infer)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:698(_try_cast)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:161(__init__)
       22    0.000    0.000    0.000    0.000 {pandas._libs.lib.infer_datetimelike_array}
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_dtype.py:321(_name_get)
     12/4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:174(getwidth)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:323(__init__)
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:354(cache_from_source)
        1    0.000    0.000    0.000    0.000 {built-in method pandas._libs.missing.isnaobj}
        4    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3463(__getitem__)
       48    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_list_like}
      104    0.000    0.000    0.000    0.000 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:962(<listcomp>)
        1    0.000    0.000    0.003    0.003 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:601(get_handle)
       92    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:164(__getitem__)
        1    0.000    0.000    0.000    0.000 {method 'get_slice' of 'pandas._libs.internals.BlockManager' objects}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1255(__init__)
      108    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:222(_verbose_message)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:227(_isna_array)
       44    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1587(_is_dtype_type)
        9    0.000    0.000    0.000    0.000 {built-in method _thread.allocate_lock}
        1    0.000    0.000    0.006    0.006 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:306(_save_chunk)
        1    0.000    0.000    0.000    0.000 {pandas._libs.lib.infer_dtype}
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:120(_take_nd_ndarray)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:536(_compile_info)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:759(compile)
       29    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap_external>:135(_path_stat)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:5613(_cmp_method)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:253(apply)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:596(_homogenize)
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:1991(get_block_type)
        4    0.000    0.000    0.004    0.001 <frozen importlib._bootstrap>:956(_find_and_load_unlocked)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1478(_get_optional_kwargs)
       47    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:233(__next)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:937(parse)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:560(__init__)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5577(__setattr__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:435(_parse_sub)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5192(equals)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\weakref.py:517(__init__)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:477(_init_module_attrs)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1549(_fill_cache)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:845(_engine)
        6    0.000    0.000    0.000    0.000 {method 'reduce' of 'numpy.ufunc' objects}
        2    0.000    0.000    0.000    0.000 {method 'argsort' of 'numpy.ndarray' objects}
        2    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\__init__.py:70(search_function)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:554(_take_preprocess_indexer_and_fill_value)
       37    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1308(register)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1951(create_block_manager_from_column_arrays)
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap>:650(_load_unlocked)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:109(_get_single_key)
       15    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:1067(convert)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1026(iget)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:221(require)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1106(take)
        8    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7004(ensure_index)
       19    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(copyto)
        9    0.000    0.000    0.000    0.000 {method 'copy' of 'numpy.ndarray' objects}
       15    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1038(iget_values)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:689(spec_from_file_location)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:166(_consolidate_key)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1689(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:4719(reindex)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:586(_format_args)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:32(seterr)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:630(_sanitize_ndim)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:934(_list_of_dict_to_arrays)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:130(filterwarnings)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1962(construct_1d_object_array_from_listlike)
       15    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:389(is_timedelta64_dtype)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:229(asarray_tuplesafe)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\array_ops.py:227(comparison_op)
        1    0.000    0.000    0.000    0.000 {method 'setsockopt' of '_socket.socket' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:251(_get_filepath_or_buffer)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3609(take)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:1116(take_nd)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1638(__init__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1398(_add_action)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:654(_simple_new)
        1    0.000    0.000    0.028    0.028 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\decoder.py:332(decode)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:193(_new_conn)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:127(_get_option)
        2    0.000    0.000    0.000    0.000 {method 'nonzero' of 'numpy.ndarray' objects}
        1    0.000    0.000    0.028    0.028 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\__init__.py:299(loads)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1240(is_float_dtype)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2043(new_block)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:27(create_connection)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:634(reindex_indexer)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:288(_get_take_nd_function_cached)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:872(take)
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:842(exec_module)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7123(_maybe_cast_data_without_dtype)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:631(to_native_types)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:102(arrays_to_mgr)
        1    0.000    0.000    0.010    0.010 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:236(save)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:47(CSVFormatter)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:103(release)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:1962(maybe_coerce_values)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:638(_compile_bytecode)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:222(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexers\utils.py:457(check_array_indexer)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:167(_simple_new)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:286(tell)
        1    0.000    0.000    0.010    0.010 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:1131(to_csv)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:212(_expand_lang)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:538(find)
       39    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:172(append)
       18    0.000    0.000    0.000    0.000 {method 'format' of 'str' objects}
       21    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:64(_relax_case)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:254(get)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:816(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1809(_parse_known_args)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:167(_path_isabs)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:313(__call__)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:78(acquire)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:57(take_nd)
       32    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:909(__len__)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2088(_consolidate)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\__init__.py:43(normalize_encoding)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:276(_optimize_charset)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:971(_finalize_columns_and_data)
        2    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis1_int64_int64}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:938(__and__)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:589(_get_root)
        2    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis1_object_object}
        2    0.000    0.000    0.000    0.000 {built-in method numpy.arange}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:458(__enter__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:579(translation)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:438(ensure_wrapped_if_datetimelike)
        2    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3530(_getitem_bool_array)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:229(_new_pool)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:379(extract_array)
        9    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:79(_unpack_uint32)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:553(_classify_pyc)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5009(_reindex_with_indexers)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:809(__init__)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1493(_get_spec)
        1    0.000    0.000    0.001    0.001 D:\Project\噪音优化\ECG-Analysis\apps\api\tests\api_test.py:508(parse_arguments)
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_dtype.py:307(_name_includes_bit_suffix)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\flags.py:47(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:50(__init__)
        1    0.000    0.000    0.000    0.000 {method 'writerow' of '_csv.writer' objects}
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:127(_path_split)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\_distutils_hack\__init__.py:102(find_spec)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:161(is_object_dtype)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:53(shape)
        2    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1693(_consolidate_inplace)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:238(_new_conn)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1774(parse_known_args)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:549(module_from_spec)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:64(parse_parts)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:132(geterr)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:147(<lambda>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3908(_slice)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:689(<listcomp>)
       44    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:392(__subclasshook__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:33(__init__)
        2    0.000    0.000    0.000    0.000 {method 'get_loc' of 'pandas._libs.index.IndexEngine' objects}
        2    0.000    0.000    0.001    0.000 {built-in method builtins.__import__}
        2    0.000    0.000    0.002    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:1109(_is_binary_mode)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\re.py:250(compile)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:218(_acquireLock)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numerictypes.py:360(issubdtype)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\array_ops.py:58(comp_method_OBJECT_ARRAY)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:58(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1451(_format_native_types)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:130(iloc)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:338(_format_native_types)
       15    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2006(_grouping_func)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:556(check_parent_directory)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:603(_get_deprecated_option)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3411(_ixs)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:640(name)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\types.py:171(__get__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:598(_code)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:1(<module>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:181(_add_filter)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:970(from_blocks)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:798(to_arrays)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:2988(_construct_result)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3906(_box_col_values)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2121(extend_blocks)
        1    0.000    0.000    0.000    0.000 {pandas._libs.lib.array_equivalent_object}
        2    0.000    0.000    0.000    0.000 {built-in method _imp.is_builtin}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:631(__new__)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:160(__len__)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:145(classes)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5632(_protect_consolidate)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:572(na_value_for_dtype)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3923(_get_item_cache)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:223(encoded_labels)
        2    0.000    0.000    0.000    0.000 {method 'split' of 'bytes' objects}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:224(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:189(_data)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1683(_consolidate_check)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:168(_number_format)
       24    0.000    0.000    0.000    0.000 {pandas._libs.algos.ensure_object}
       18    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:867(__exit__)
        4    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap_external>:1399(find_spec)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:355(_escape)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3708(_take_with_is_copy)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:358(iget)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2057(check_ndim)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:578(copy)
    22/20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:14(asarray)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:390(is_dataclass)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:515(_maybe_promote)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:560(_get_axis)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1567(__init__)
        2    0.000    0.000    0.000    0.000 {method 'take' of 'numpy.ndarray' objects}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:147(__enter__)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:484(_get_cached)
       16    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:321(is_hashable)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:667(_sanitize_str_dtypes)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:552(require_length_match)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\compat\_optional.py:87(import_optional_dependency)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:4788(reindex)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:505(nested_data_to_arrays)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:94(__new__)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numerictypes.py:286(issubclass_)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:944(_getitem_slice)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:495(_array_equivalent_object)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:468(maybe_promote)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:3585(get_loc)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1851(dtype)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:575(dtype)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:134(__init__)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1274(is_bool_dtype)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:268(_isna_string_dtype)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:554(_dtype_to_subclass)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\flags.py:83(allows_duplicate_labels)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:172(from_float)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:144(_initialize_columns)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1589(_add_action)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:461(_get_literal_prefix)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1039(__new__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2480(_get_formatter)
       18    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:129(<genexpr>)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:249(_compile_charset)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5037(__getitem__)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7099(maybe_extract_name)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:1527(_get_slice_axis)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\idna.py:147(encode)
       10    0.000    0.000    0.000    0.000 {built-in method builtins.setattr}
       18    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:863(__enter__)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:145(_path_is_mode_type)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:571(_select_options)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\idna.py:300(getregentry)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:1(<module>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2357(check_bool_indexer)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1317(_path_hooks)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:391(_splitnetloc)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:581(is_dtype_equal)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:391(array_equivalent)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2093(<lambda>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:103(close)
        3    0.000    0.000    0.000    0.000 {built-in method builtins.exec}
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:376(cached)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:570(_metavar_formatter)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:176(cb)
        1    0.000    0.000    0.000    0.000 <frozen zipimport>:63(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:77(join)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:326(_get_take_nd_function)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:687(_maybe_repeat)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:638(copy)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1075(path_stats)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:111(__init__)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:834(_reset_identity)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2486(check_deprecated_indexers)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:82(atleast_2d)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:267(make_block_same_class)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:851(__init__)
        5    0.000    0.000    0.000    0.000 {method 'astype' of 'numpy.ndarray' objects}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1004(__init__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:853(quote_plus)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1148(needs_i8_conversion)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:626(is_valid_na_for_dtype)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\common.py:55(new_method)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:954(__getitem__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2116(<listcomp>)
        2    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_all_arraylike}
        1    0.000    0.000    0.000    0.000 {built-in method _csv.writer}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:342(__init__)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:800(find_spec)
        5    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:1017(_handle_fromlist)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:223(vstack)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1416(is_1d_only_ea_dtype)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:546(_get_axis_number)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1312(_registry_get)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1459(__init__)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:255(__call__)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:642(_warn_if_deprecated)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4123(reindex)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1417(setLevel)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:497(infer_compression)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1747(_add_action)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:35(_new_module)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:151(__exit__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:102(__setitem__)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:298(<setcomp>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\algorithms.py:1356(take)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:4652(_reindex_columns)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1514(_pop_action_class)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:423(_simple)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:901(getaddrinfo)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1483(is_ea_or_datetimelike_dtype)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5561(__getattr__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:522(equals)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:1073(<listcomp>)
        2    0.000    0.000    0.000    0.000 {method 'remove' of 'list' objects}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:660(dgettext)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:195(stringify_path)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:516(_check_name_wrapper)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:586(_validate_timestamp_pyc)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_methods.py:53(_any)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:105(is_bool_indexer)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:156(blknos)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1675(is_consolidated)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1862(internal_values)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1552(get_dtype)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:884(__len__)
       23    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4834(_values)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5650(f)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:155(_expand_user)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:27(__init__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:595(isstring)
        4    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(concatenate)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1721(validate_all_hashable)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:81(__init__)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1675(getEffectiveLevel)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1417(is_dir)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\locale.py:384(normalize)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\base.py:744(__iter__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:4952(<genexpr>)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:347(dtype)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:252(make_block)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:82(_have_working_poll)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:268(_save_header)
        6    0.000    0.000    0.000    0.000 {method 'any' of 'numpy.ndarray' objects}
        2    0.000    0.000    0.000    0.000 {built-in method builtins.locals}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:433(__enter__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1714(__init__)
        4    0.000    0.000    0.000    0.000 {built-in method numpy.seterrobj}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:725(find_spec)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:437(__init__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:921(fix_flags)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:533(is_string_or_object_np_dtype)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\util\_validators.py:258(validate_axis_style_args)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6298(_maybe_cast_indexer)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:566(_get_block_manager_axis)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1745(from_array)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:542(_set_axis)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:278(connect)
        4    0.000    0.000    0.000    0.000 {built-in method nt._path_splitroot}
       26    0.000    0.000    0.000    0.000 {built-in method _imp.acquire_lock}
       26    0.000    0.000    0.000    0.000 {built-in method _imp.release_lock}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:154(_path_isfile)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:76(__init__)
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_dtype.py:24(_kind_name)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1042(is_numeric_v_string_like)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1740(<genexpr>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\api.py:331(default_index)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:55(<genexpr>)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:590(name)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:140(has_mi_columns)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:579(format)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1518(_get_handler)
        2    0.000    0.000    0.000    0.000 {built-in method pandas._libs.writers.word_len}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1590(path_hook_for_FileFinder)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:309(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:477(__exit__)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1429(is_extension_array_dtype)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:184(is_array_like)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:982(view)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:4997(_needs_reindex_multi)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:127(is_url)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:202(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:121(getregentry)
        1    0.000    0.000    0.006    0.006 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:263(_save)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:312(<listcomp>)
        4    0.000    0.000    0.000    0.000 {built-in method _sre.compile}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:492(_get_charset_prefix)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:227(_releaseLock)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:658(_parse_args)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:678(_from_parts)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\fromnumeric.py:999(argsort)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:160(cast_scalar_indexer)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\arraylike.py:38(__eq__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:890(__array__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2660(_isnan)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:514(_construct_axes_from_arguments)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3925(_set_is_copy)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:475(get_adjustment)
        1    0.000    0.000    0.001    0.001 {built-in method _codecs.lookup}
        4    0.000    0.000    0.000    0.000 {built-in method _imp.is_frozen}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:146(splitroot)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\multiarray.py:1043(copyto)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:328(attrs)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5646(_consolidate_inplace)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2032(new_block_2d)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:121(_get_index_label_from_obj)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:200(_has_aliases)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:211(_call_with_frames_removed)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4987(__contains__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1388(add_argument_group)
       28    0.000    0.000    0.000    0.000 {built-in method builtins.callable}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4013(_validate_positional_slice)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:596(copy_func)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:95(wait_for_socket)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1954(consume_positionals)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1578(<setcomp>)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:453(_get_iscased)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\fromnumeric.py:52(_wrapfunc)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:786(is_unsigned_integer_dtype)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:919(__getitem__)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:354(dtype)
        8    0.000    0.000    0.000    0.000 {built-in method numpy.geterrobj}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:881(_find_spec_legacy)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:97(_intenum_converter)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:718(__str__)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:86(asanyarray)
        2    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(vstack)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2440(is_object)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:238(fill_value)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:172(blklocs)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:687(_values)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:1149(_normalize_host)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1527(_check_conflict)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:208(write_cols)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:72(_check_methods)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:249(match)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:399(_checknetloc)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1747(pandas_dtype)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1711(sanitize_to_nanoseconds)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2352(is_floating)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4244(_maybe_preserve_names)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:1130(_get_binary_io_classes)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:93(_set_socket_options)
        8    0.000    0.000    0.000    0.000 {method 'fileno' of '_io.TextIOWrapper' objects}
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\importlib\__init__.py:109(import_module)
        2    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(argsort)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:803(is_)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:1238(_set_as_cached)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:349(flags)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2323(convert_to_index_sliceable)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:468(to_native_types)
       13    0.000    0.000    0.000    0.000 {method 'isalnum' of 'str' objects}
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:81(groups)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:735(gettext)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:630(_translate_key)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\common.py:75(get_op_result_name)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:1413(__len__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:534(treat_as_nested)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:825(__array__)
        3    0.000    0.000    0.000    0.000 {built-in method _imp._fix_co_filename}
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1276(disable)
        2    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(atleast_2d)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:155(<lambda>)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:507(_maybe_promote_cached)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:346(apply_if_callable)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2650(_na_value)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:45(__len__)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:916(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:204(_need_to_save_header)
       21    0.000    0.000    0.000    0.000 {built-in method _sre.unicode_iscased}
        9    0.000    0.000    0.000    0.000 {built-in method from_bytes}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:143(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:945(parent)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1049(_init)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:680(is_integer_dtype)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:805(is_empty_data)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6313(_validate_indexer)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:618(consolidate)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:574(<listcomp>)
        1    0.000    0.000    0.001    0.001 <frozen importlib._bootstrap>:1002(_gcd_import)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:753(value)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:432(_generate_overlap_table)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:438(__exit__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:732(is_signed_integer_dtype)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:578(_constructor)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:554(_get_axis_name)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:785(truncate)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:239(is_fsspec_url)
        6    0.000    0.000    0.000    0.000 {method 'ravel' of 'numpy.ndarray' objects}
        2    0.000    0.000    0.000    0.000 {method 'reshape' of 'numpy.ndarray' objects}
        4    0.000    0.000    0.000    0.000 {method 'find' of 'bytearray' objects}
       14    0.000    0.000    0.000    0.000 {method 'add' of 'set' objects}
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:168(__setitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\util\_decorators.py:322(wrapper)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:454(ensure_dtype_can_hold_na)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\missing.py:911(clean_reindex_fill_method)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\base.py:326(ndim)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:4615(_reindex_axes)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:679(_initialize_justify)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:685(_initialize_columns)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:123(__exit__)
       19    0.000    0.000    0.000    0.000 {built-in method _sre.unicode_tolower}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1193(stat)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\dataclasses.py:1045(is_dataclass)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:130(_get_index_label_flat)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:219(_vhstack_dispatcher)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\flags.py:51(allows_duplicate_labels)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:1490(_getitem_axis)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:728(_calc_max_rows_fitted)
        8    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1465(<genexpr>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:429(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2604(inferred_type)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:439(_view)
        1    0.000    0.000    0.000    0.000 <string>:2(__init__)
        6    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_integer}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:701(_format_parsed_parts)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:728(__fspath__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\dispatch.py:11(should_extension_dispatch)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5279(identical)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2125(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:717(_calc_max_cols_fitted)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:761(_is_in_terminal)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:1051(_convert_object_array)
        1    0.000    0.000    0.000    0.000 {method 'fill' of 'numpy.ndarray' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:221(dirname)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1200(_validate_host)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:150(classes_and_not_datetimelike)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:222(count_not_none)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:785(_view)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2685(isna)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:241(start)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:244(mgr_locs)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:222(items)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:993(_validate_or_indexify_columns)
        8    0.000    0.000    0.000    0.000 {pandas._libs.lib.item_from_zerodim}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:186(__init__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:302(__subclasshook__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:406(__subclasshook__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:193(_checkLevel)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4860(_get_engine_target)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1806(_block)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:1055(_maybe_memory_map)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:103(allowed_gai_family)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1031(__init__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:945(dtype)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1658(name)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4240(_wrap_reindex_result)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:264(stop)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:599(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:423(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:743(__len__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:997(raise_for_status)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1759(_get_positional_actions)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2106(_match_arguments_partial)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:179(data_index)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:26(IncrementalEncoder)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:331(getstate)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:104(header)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:691(_from_parsed_parts)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:208(_arrays_for_stack_dispatcher)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\six.py:184(find_module)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:540(_ensure_array)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:462(get_compression_method)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:959(<genexpr>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:909(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2414(_get_value)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:22(netrc)
        8    0.000    0.000    0.000    0.000 {pandas._libs.algos.ensure_platform_int}
        5    0.000    0.000    0.000    0.000 {method 'reverse' of 'list' objects}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:159(_path_isdir)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:162(__delitem__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\multiarray.py:143(concatenate)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:163(_initialize_chunksize)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:294(is_named_tuple)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\compat\numpy\function.py:49(__call__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2632(_is_multi)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:1019(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:233(mgr_to_mgr)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:92(na_rep)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:49(IncrementalDecoder)
        6    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_float}
        1    0.000    0.000    0.000    0.000 {built-in method _codecs.lookup_error}
        3    0.000    0.000    0.000    0.000 {built-in method sys.intern}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:937(_sanity_check)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:839(create_module)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2018(getLogger)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:543(<dictcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:99(_can_hold_na)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:287(step)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:661(_initialize_sparsify)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:693(_initialize_colspace)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:134(_initialize_quotechar)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:886(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:112(_initialize_index_label)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:10(NetrcParseError)
        1    0.000    0.000    0.000    0.000 {built-in method atexit.register}
        2    0.000    0.000    0.000    0.000 {method 'insert' of 'list' objects}
        6    0.000    0.000    0.000    0.000 {built-in method _warnings._filters_mutated}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1029(get_filename)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:260(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\_distutils_hack\__init__.py:109(<lambda>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:456(<genexpr>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1132(_maybe_disallow_fill)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2473(need_slice)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1760(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1767(parse_args)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:96(float_format)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:193(nlevels)
        4    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_iterator}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:242(_init)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:98(is_file_like)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\missing.py:107(clean_fill_method)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:229(disallow_kwargs)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7063(ensure_has_len)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:183(_constructor)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:331(_is_all_dates)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:282(<dictcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_request_methods.py:51(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:183(host)
        1    0.000    0.000    0.000    0.000 {built-in method _socket.getdefaulttimeout}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:397(has_location)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:862(_get_hostport)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:226(<genexpr>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:629(is_truncated_horizontally)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:523(_constructor)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:100(decimal)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:108(index)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:604(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:97(StreamReader)
        1    0.000    0.000    0.000    0.000 {built-in method _stat.S_ISDIR}
        2    0.000    0.000    0.000    0.000 {method 'isascii' of 'str' objects}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:78(_atleast_2d_dispatcher)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\fromnumeric.py:995(_argsort_dispatcher)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:633(is_truncated_vertically)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:666(_initialize_formatters)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:747(_adjust_max_rows)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1240(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:85(StreamWriter)
        1    0.000    0.000    0.000    0.000 {built-in method sys.audit}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\importlib_metadata\_compat.py:44(find_spec)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1906(nlevels)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:120(__enter__)
        1    0.000    0.000    0.000    0.000 {built-in method builtins.iter}
        1    0.000    0.000    0.000    0.000 {method 'disable' of '_lsprof.Profiler' objects}


