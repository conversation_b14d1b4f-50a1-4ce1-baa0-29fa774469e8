# ECG Analysis System

心电图分析系统，支持心律失常检测、睡眠监测等功能。

## 功能特性

### 核心功能
- **心律失常检测**：基于ECG信号的心律失常诊断
- **睡眠监测**：基于ECG+呼吸波的睡眠阶段检测（W/N1-3/R）
- **呼吸分析**：呼吸频率计算和OSA风险评估
- **健康指标**：心率变异性、压力、疲劳等指标计算

### 新增功能（v2024.12）
- **睡眠检测集成**：将睡眠监测模型完全集成到现有API
- **呼吸波分析**：支持250Hz采样率的呼吸波数据处理
- **三分类睡眠阶段**：清醒(W)、非REM睡眠(N1-3)、REM睡眠(R)

## 技术架构

### 后端框架
- **Django 3.2.14**：Web框架
- **MySQL**：数据存储
- **Redis**：缓存服务

### 信号处理
- **NumPy 1.22.0**：数值计算
- **SciPy 1.6.0**：信号处理
- **BioSPPy 2.1.2**：生物信号处理
- **PyHRV 0.4.1**：心率变异性分析

### 机器学习
- **PyTorch 2.4.1**：深度学习框架（睡眠检测）
- **scikit-learn 1.6.1**：传统机器学习
- **TorchVision 0.19.1**：PyTorch视觉库

## 安装部署

### 环境要求
- Python 3.8+
- MySQL 5.7+
- Redis 6.0+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd ECG-Analysis
```

2. **创建虚拟环境**
```bash
python -m venv myenv38
# Windows
myenv38\Scripts\activate
# Linux/Mac
source myenv38/bin/activate
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **数据库配置**
```bash
python manage.py migrate
```

5. **启动服务**
```bash
python manage.py runserver
```

## API接口

### 心律失常检测 + 睡眠监测
**端点**：`POST /api/diagnose/arrhythmia/`

**请求参数**：
```json
{
    "signal": [ECG数据数组],
    "fs": 采样率,
    "breatheData": [呼吸波数据数组, 可选],
    "union_id": "用户ID",
    "adc_gain": 1,
    "adc_zero": 0
}
```

**返回字段**：
- `ArrhythmiaDiagnosis`：心律失常诊断结果
- `SleepStage`：睡眠阶段（0=清醒，1=非REM，2=REM）
- `OSARisk`：OSA风险（0-1，-1表示时长不足）
- `RespiratoryRate`：呼吸频率（次/分钟）
- `HealthMetrics`：健康指标
- `PQRSTC`：心电参数

## 睡眠检测详细说明

### 模型架构
- **EnhancedSleepModel**：基于注意力机制的融合模型
- **输入特征**：ECG特征(12维) + 呼吸特征(8维)
- **输出类别**：3分类（W/N1-3/R）

### 数据要求
- **最小时长**：60秒（推荐≥2分钟）
- **ECG采样率**：可变（通过fs参数指定）
- **呼吸波采样率**：250Hz
- **数据格式**：支持数值数组或字符串数组

### 权重文件
- **位置**：`apps/analysis/breathe/sleep/model/`
- **优先级**：`finetuned.pth` > `best_model.pth`
- **备用路径**：`sleep_toolkit/` 目录

## 项目结构

```
ECG-Analysis/
├── apps/
│   ├── analysis/
│   │   ├── breathe/
│   │   │   ├── business.py          # 数据存储
│   │   │   ├── breathe_analyzer.py  # 呼吸分析
│   │   │   ├── sleep_infer.py       # 睡眠推理
│   │   │   └── sleep/
│   │   │       └── model/           # 睡眠模型和权重
│   │   └── ...
│   ├── api/
│   │   └── diagnose/
│   │       └── interface.py         # 主要API接口
│   └── models/
├── requirements.txt                 # 依赖列表
└── manage.py
```

## 更新日志

### v2024.12
- ✅ 集成睡眠监测模型到现有API
- ✅ 支持250Hz呼吸波数据处理
- ✅ 添加PyTorch深度学习依赖
- ✅ 优化文件结构和代码组织
- ✅ 更新依赖文档

## 开发团队

- 睡眠检测模型集成：2024年12月
- 原ECG分析系统：历史版本

## 许可证

[项目许可证信息]
