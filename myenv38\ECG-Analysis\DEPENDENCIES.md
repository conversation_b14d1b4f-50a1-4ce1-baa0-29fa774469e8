# 依赖说明文档

## 依赖更新记录

### 2024年12月 - 睡眠检测功能集成

为支持睡眠监测功能，新增以下依赖：

#### 新增依赖
- **torch==2.4.1**：PyTorch深度学习框架，用于睡眠检测模型推理
- **torchvision==0.19.1**：PyTorch视觉库（模型依赖）
- **torchsummary==1.5.1**：模型结构摘要工具
- **typing_extensions==4.13.2**：类型扩展（解决PyTorch兼容性问题）

#### 依赖分类

##### 核心框架
```
Django==3.2.14          # Web框架
mysqlclient==2.2.4       # MySQL数据库连接器
redis~=6.0.0             # Redis缓存
```

##### 科学计算
```
numpy==1.22.0            # 数值计算基础库
scipy==1.6.0             # 科学计算和信号处理
pandas==1.4.4            # 数据处理
biosppy==2.1.2           # 生物信号处理专用库
pyhrv~=0.4.1             # 心率变异性分析
```

##### 机器学习
```
scikit-learn~=1.6.1      # 传统机器学习算法
joblib==1.4.2            # 并行计算和模型序列化
```

##### 深度学习（新增）
```
torch==2.4.1             # PyTorch深度学习框架
torchvision==0.19.1      # PyTorch视觉库
torchsummary==1.5.1      # 模型摘要工具
```

##### 工具库
```
loguru==0.7.2            # 日志记录
requests~=2.32.4         # HTTP请求
typing_extensions==4.13.2 # 类型扩展（PyTorch兼容性）
```

## 版本兼容性

### Python版本
- **要求**：Python 3.8+
- **推荐**：Python 3.8-3.11
- **测试环境**：Python 3.8

### PyTorch版本选择
- **torch 2.4.1**：最新稳定版，支持Python 3.8
- **CUDA支持**：如需GPU加速，请安装CUDA版本
- **CPU版本**：当前安装为CPU版本，适合推理使用

### 依赖冲突解决

#### typing_extensions 升级
**问题**：原版本*******不支持PyTorch所需的`ParamSpec`
**解决**：升级到4.13.2版本
```bash
pip install --upgrade typing_extensions
```

#### 潜在冲突警告
安装过程中可能出现以下警告（不影响功能）：
- `torchvision` 与其他包的版本兼容性警告
- `typing_extensions` 版本冲突警告

## 安装指南

### 完整安装
```bash
# 安装所有依赖
pip install -r requirements.txt
```

### 分步安装（推荐）
```bash
# 1. 核心依赖
pip install Django==3.2.14 mysqlclient==2.2.4

# 2. 科学计算
pip install numpy==1.22.0 scipy==1.6.0 pandas==1.4.4

# 3. 信号处理
pip install biosppy==2.1.2 pyhrv~=0.4.1

# 4. 机器学习
pip install scikit-learn~=1.6.1 joblib==1.4.2

# 5. 深度学习（新增）
pip install torch==2.4.1 torchvision==0.19.1 torchsummary==1.5.1

# 6. 工具库
pip install loguru==0.7.2 requests~=2.32.4 typing_extensions==4.13.2
```

### GPU版本安装（可选）
如需GPU加速睡眠检测：
```bash
# 卸载CPU版本
pip uninstall torch torchvision

# 安装CUDA版本（根据CUDA版本选择）
pip install torch==2.4.1+cu118 torchvision==0.19.1+cu118 --index-url https://download.pytorch.org/whl/cu118
```

## 功能依赖映射

### 心律失常检测
- numpy, scipy：信号处理
- biosppy：ECG信号分析
- pyhrv：心率变异性
- scikit-learn：分类算法

### 睡眠检测（新增）
- torch：深度学习模型推理
- numpy, scipy：特征提取
- typing_extensions：类型支持

### 呼吸分析
- scipy：峰值检测
- numpy：数值计算

## 性能考虑

### 内存使用
- **PyTorch模型**：约50-100MB内存占用
- **权重文件**：约10-20MB磁盘空间

### 推理速度
- **CPU推理**：单次睡眠检测约100-500ms
- **GPU推理**：可提升2-5倍速度（如有GPU）

## 故障排除

### 常见问题

1. **ImportError: cannot import name 'ParamSpec'**
   ```bash
   pip install --upgrade typing_extensions
   ```

2. **torch模块找不到**
   ```bash
   pip install torch==2.4.1
   ```

3. **CUDA相关错误**
   - 确认是否需要GPU版本
   - 检查CUDA版本兼容性

### 验证安装
```python
import torch
import numpy as np
import scipy
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
```

## 更新建议

### 定期更新
- **安全更新**：及时更新Django等框架
- **功能更新**：根据需要更新科学计算库
- **谨慎更新**：PyTorch等深度学习库需测试兼容性

### 版本锁定
当前requirements.txt已锁定关键版本，确保环境一致性。
