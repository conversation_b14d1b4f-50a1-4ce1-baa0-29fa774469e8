import h5py
import json
import requests
import sys
import os
import pandas as pd
import logging
import cProfile  # Re-enabled
import pstats  # Re-enabled
from pstats import SortKey  # Re-enabled
import io  # Re-enabled
import time
import argparse
import urllib3
from qiniu import Auth
import ast
import numpy as np

# 移除本地信号处理函数的引入
# from apps.signal_analysis.signal import get_available_signals

# 禁用TensorFlow的warning和error输出，避免Tkinter相关错误
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=全部打印, 1=不打印INFO, 2=不打印WARNING, 3=不打印ERROR

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 七牛云配置
QINIU = {
    'test': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://test.upload.weiheyixue.com'
    },
    'prod': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://upload.weiheyixue.com'
    }
}

# 尝试禁用Tkinter相关功能
try:
    import matplotlib

    matplotlib.use('Agg')  # 使用非交互式后端，避免Tkinter错误
except ImportError:
    pass

# 修改日志格式,只输出消息内容
formatter = logging.Formatter('%(message)s')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(message)s')

# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录的路径
project_root = os.path.abspath(os.path.join(current_dir, '../../..'))
# 将项目根目录添加到 sys.path
sys.path.append(project_root)

# 创建全局会话
session = requests.Session()

# 设置连接池最大连接数
adapter = requests.adapters.HTTPAdapter(
    pool_connections=100,  # 连接池连接数
    pool_maxsize=100,  # 连接池最大连接数
    max_retries=3  # 最大重试次数
)
session.mount('http://', adapter)
session.mount('https://', adapter)

# 全局变量
TOKEN_EXPIRY_TIME = 3600  # token有效期(秒)
TOKEN_REFRESH_THRESHOLD = 3300  # token刷新阈值(秒)
token_obtain_time = 0  # token获取时间
token_value = None  # 全局token值
REQUEST_INTERVAL = 0.1  # 请求间隔(秒)
# MAX_ECG_POINTS = 5000  # 最大ECG数据点数 (用户要求不再截断)

ERROR_CODE_MAP = {
    401: "信号噪声过高",
    402: "算法内部错误",
    403: "心律失常诊断错误",
    4: "Token过期或无效",
    6: "数据格式或内容无效",  # 添加错误代码6的定义
    2: "API内部异常",  # 添加错误代码2的映射
    # ... 其他错误代码 ...
}

def get_qiniu_data(es_key, environment='prod', timeout=10):
    """从七牛云获取ECG数据，参考qiniu_helper.py的实现"""
    try:
        access_key = QINIU[environment]['access_key']
        secret_key = QINIU[environment]['secret_key']
        domain_prefix = QINIU[environment]['domain_prefix']

        # 构建鉴权对象
        q = Auth(access_key, secret_key)

        # 尝试不同的路径前缀，参考api_test_oss.py的实现
        path_prefixes = ['/ecg/', '/ecgError/', '/']

        for prefix in path_prefixes:
            file_path = prefix + es_key

            try:
                # 构建私有空间的下载链接
                private_url = q.private_download_url(domain_prefix + file_path)

                # 使用requests库下载文件，添加SSL验证禁用
                response = requests.get(private_url, verify=False, timeout=timeout)

                if response.status_code == 200:
                    # 成功获取数据
                    data = json.loads(response.content)
                    logging.info(f"成功从七牛云获取数据，路径: {file_path}")
                    return data
                elif response.status_code == 404:
                    # 404错误，尝试下一个路径前缀
                    logging.debug(f"路径 {file_path} 返回404，尝试下一个路径")
                    continue
                else:
                    # 其他错误状态码
                    logging.warning(f"七牛云请求失败: {response.status_code}, 路径: {file_path}")
                    continue

            except requests.exceptions.RequestException as e:
                logging.warning(f"七牛云请求异常: {e}, 路径: {file_path}")
                continue
            except json.JSONDecodeError as e:
                logging.error(f"JSON解析失败: {e}, 路径: {file_path}")
                continue

        # 所有路径前缀都尝试过了，仍然没有找到数据
        logging.error(f"所有路径都尝试过了，未找到es_key: {es_key}")
        return None

    except Exception as e:
        logging.error(f"获取七牛云数据时发生错误: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return None

def process_es_key_diagnosis(es_key, environment='prod', sampling_rate=500, token=None):
    """处理单个es_key的诊断"""
    logging.info(f"开始处理es_key: {es_key}")

    # 从七牛云获取数据
    qiniu_data = get_qiniu_data(es_key, environment)

    if not qiniu_data:
        logging.error(f"无法从七牛云获取es_key '{es_key}' 的数据")
        return {
            'es_key': es_key,
            'status': '失败',
            'error_message': '无法从七牛云获取数据',
            'environment': environment
        }

    logging.info(f"成功从七牛云获取数据，数据类型: {type(qiniu_data)}")

    # 提取ECG数据，参考api_test_oss.py的实现
    ecg_data = None
    ecg_field_used = None

    if isinstance(qiniu_data, dict):
        # 优先使用ecg，然后是ecgII，修改默认优先级
        for field in ['ecg', 'ecgII', 'ecgI', 'signal']:
            if field in qiniu_data and qiniu_data[field]:
                ecg_data = qiniu_data[field]
                ecg_field_used = field
                logging.info(f"使用字段 '{field}' 作为ECG数据源")
                break

    if not ecg_data:
        logging.error(f"在七牛云数据中未找到ECG信号数据")
        return {
            'es_key': es_key,
            'status': '失败',
            'error_message': '七牛云数据中未找到ECG信号',
            'environment': environment,
            'qiniu_data_keys': list(qiniu_data.keys()) if isinstance(qiniu_data, dict) else 'N/A'
        }

    # 处理ECG数据，参考api_test_oss.py的数据处理方式
    try:
        if isinstance(ecg_data, str):
            # 使用ast.literal_eval解析字符串，这是api_test_oss.py中使用的方法
            try:
                ecg_data = ast.literal_eval(ecg_data)
                logging.info(f"使用ast.literal_eval成功解析ECG数据字符串")
            except (ValueError, SyntaxError) as e:
                # 如果ast.literal_eval失败，尝试JSON解析
                try:
                    if ecg_data.startswith('[') and ecg_data.endswith(']'):
                        ecg_data = json.loads(ecg_data)
                    else:
                        ecg_data = [float(x.strip()) for x in ecg_data.split(',') if x.strip()]
                    logging.info(f"使用JSON解析成功解析ECG数据字符串")
                except (ValueError, json.JSONDecodeError) as e2:
                    logging.error(f"无法解析ECG数据字符串: ast错误={e}, json错误={e2}")
                    return {
                        'es_key': es_key,
                        'status': '失败',
                        'error_message': f'ECG数据格式错误: {e2}',
                        'environment': environment
                    }

        # 转换为numpy数组，然后转回列表，参考api_test_oss.py
        if not isinstance(ecg_data, list):
            try:
                ecg_data = list(ecg_data)
            except Exception as e:
                logging.error(f"无法转换ECG数据为列表: {e}")
                return {
                    'es_key': es_key,
                    'status': '失败',
                    'error_message': f'ECG数据类型错误: {e}',
                    'environment': environment
                }

        # 使用numpy进行数据验证和处理
        ecg_array = np.array(ecg_data)
        logging.info(f"ECG数据转换为numpy数组，形状: {ecg_array.shape}, 数据类型: {ecg_array.dtype}")

    except Exception as e:
        logging.error(f"ECG数据处理失败: {e}")
        return {
            'es_key': es_key,
            'status': '失败',
            'error_message': f'ECG数据处理错误: {e}',
            'environment': environment
        }

    # 数据清理，参考api_test_oss.py的实现
    try:
        # 使用numpy进行数据清理
        ecg_array = np.array(ecg_data, dtype=float)

        # 处理无效值：NaN、inf、-inf
        invalid_mask = ~np.isfinite(ecg_array)
        if np.any(invalid_mask):
            logging.warning(f"发现 {np.sum(invalid_mask)} 个无效值，将替换为0")
            ecg_array[invalid_mask] = 0

        # 转换回列表
        cleaned_ecg_data = ecg_array.tolist()

        # 检查数据范围，参考api_test_oss.py的数据验证
        data_min, data_max = np.min(ecg_array), np.max(ecg_array)
        data_std = np.std(ecg_array)
        logging.info(f"ECG数据统计: 最小值={data_min:.2f}, 最大值={data_max:.2f}, 标准差={data_std:.2f}")

        # 检查数据是否全为零或常数
        if data_std < 1e-6:
            logging.warning(f"ECG数据标准差过小({data_std})，可能是无效数据")
            return {
                'es_key': es_key,
                'status': '失败',
                'error_message': f'ECG数据标准差过小({data_std})，可能是无效数据',
                'environment': environment,
                'data_stats': {'min': data_min, 'max': data_max, 'std': data_std}
            }

    except Exception as e:
        logging.error(f"ECG数据清理失败: {e}")
        return {
            'es_key': es_key,
            'status': '失败',
            'error_message': f'ECG数据清理错误: {e}',
            'environment': environment
        }

    if not cleaned_ecg_data:
        logging.error(f"ECG数据清理后为空")
        return {
            'es_key': es_key,
            'status': '失败',
            'error_message': 'ECG数据清理后为空',
            'environment': environment
        }

    logging.info(f"ECG数据长度: {len(cleaned_ecg_data)}")

    # 检查数据长度
    min_required_samples = sampling_rate * 10  # 至少10秒数据
    if len(cleaned_ecg_data) < min_required_samples:
        logging.warning(f"ECG数据长度不足10秒 ({len(cleaned_ecg_data)} < {min_required_samples})")
        return {
            'es_key': es_key,
            'status': '失败',
            'error_message': f'数据长度不足10秒({len(cleaned_ecg_data)}<{min_required_samples})',
            'environment': environment,
            'data_length': len(cleaned_ecg_data)
        }

    # 调用API进行诊断
    logging.info(f"准备调用API进行诊断，数据长度: {len(cleaned_ecg_data)}")
    result, sna_features, raw_response = ecg_analysis(cleaned_ecg_data, sampling_rate, token)

    if result:
        # 成功获得诊断结果
        arrhythmia_diagnosis = result.get('ArrhythmiaDiagnosis', {})
        diseases = get_disease_name(arrhythmia_diagnosis)
        multi_label_diagnosis = result.get('MultiLabelDiagnosis', [])
        chinese_multi_label = get_multi_label_disease_name(multi_label_diagnosis)

        logging.info(f"诊断成功:")
        logging.info(f"  传统诊断结果: {diseases}")
        logging.info(f"  多结论诊断结果: {', '.join(chinese_multi_label) if chinese_multi_label else '无'}")

        return {
            'es_key': es_key,
            'status': '成功',
            'environment': environment,
            'data_length': len(cleaned_ecg_data),
            'diseases': diseases,
            'multi_label_diagnosis': chinese_multi_label,
            'ecg_age': result.get('ECGAge'),
            'arrhythmia_diagnosis': result.get('ArrhythmiaDiagnosis', {}),
            'health_metrics': result.get('HealthMetrics', {}),
            'pqrstc': result.get('PQRSTC', {}),
            'signal_quantity': result.get('SignalQuantity'),
            'is_noise': result.get('IsNoise', False),
            'noise_message': result.get('NoiseMessage', ''),
            'raw_response': raw_response,
            'qiniu_data': qiniu_data  # 保存原始七牛云数据
        }
    else:
        # API调用失败
        error_info = ""
        if raw_response:
            error_code = raw_response.get("code")
            error_msg = raw_response.get("msg", "")
            error_info = f"API返回错误: 代码={error_code}, 消息={error_msg}"

        logging.error(f"API诊断失败: {error_info}")
        return {
            'es_key': es_key,
            'status': '失败',
            'environment': environment,
            'data_length': len(cleaned_ecg_data),
            'error_message': error_info or 'API调用失败',
            'raw_response': raw_response,
            'qiniu_data': qiniu_data
        }

def process_es_key_batch(es_key_list, environment='prod', sampling_rate=500, token=None, output_dir=None):
    """批量处理es_key诊断，参考api_test_oss.py的批量处理方式"""
    if not es_key_list:
        logging.error("es_key列表为空")
        return []

    logging.info(f"开始批量处理 {len(es_key_list)} 个es_key")

    results = []
    success_count = 0
    failed_count = 0

    for i, es_key in enumerate(es_key_list, 1):
        logging.info(f"处理进度: {i}/{len(es_key_list)} - {es_key}")

        try:
            result = process_es_key_diagnosis(es_key, environment, sampling_rate, token)
            results.append(result)

            if result['status'] == '成功':
                success_count += 1
                logging.info(f"✓ 成功: {es_key}")
            else:
                failed_count += 1
                logging.warning(f"✗ 失败: {es_key} - {result.get('error_message', '未知错误')}")

        except Exception as e:
            failed_count += 1
            error_result = {
                'es_key': es_key,
                'status': '失败',
                'error_message': f'处理异常: {e}',
                'environment': environment
            }
            results.append(error_result)
            logging.error(f"✗ 异常: {es_key} - {e}")

    # 统计结果
    logging.info(f"批量处理完成: 成功={success_count}, 失败={failed_count}, 总计={len(es_key_list)}")

    # 保存批量结果
    if output_dir:
        try:
            os.makedirs(output_dir, exist_ok=True)

            # 保存详细结果
            results_file = os.path.join(output_dir, f"batch_diagnosis_results_{int(time.time())}.json")
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            logging.info(f"详细结果已保存到: {results_file}")

            # 保存详细汇总CSV，包含所有重要诊断字段
            summary_data = []
            for result in results:
                # 基础信息
                summary_row = {
                    'es_key': result['es_key'],
                    'status': result['status'],
                    'environment': result['environment'],
                    'error_message': result.get('error_message', ''),
                    'data_length': result.get('data_length', 0),
                }

                # 修复diseases字段格式问题
                diseases = result.get('diseases', [])
                if isinstance(diseases, list):
                    summary_row['diseases'] = ' | '.join(diseases) if diseases else ''
                else:
                    summary_row['diseases'] = str(diseases) if diseases else ''

                # 多标签诊断
                multi_label = result.get('multi_label_diagnosis', [])
                if isinstance(multi_label, list):
                    summary_row['multi_label_diagnosis'] = ' | '.join(multi_label) if multi_label else ''
                else:
                    summary_row['multi_label_diagnosis'] = str(multi_label) if multi_label else ''

                # ECG年龄和信号质量
                summary_row['ecg_age'] = result.get('ecg_age', '')
                summary_row['signal_quantity'] = result.get('signal_quantity', '')
                summary_row['is_noise'] = result.get('is_noise', False)
                summary_row['noise_message'] = result.get('noise_message', '')

                # 心律失常诊断详细信息
                arrhythmia_diagnosis = result.get('arrhythmia_diagnosis', {})
                if isinstance(arrhythmia_diagnosis, dict):
                    summary_row['rhythm_type'] = arrhythmia_diagnosis.get('RhythmType', '')
                    summary_row['heart_rate'] = arrhythmia_diagnosis.get('HeartRate', '')
                    summary_row['sna_feature'] = arrhythmia_diagnosis.get('SNA', '')
                    summary_row['pvcs_count'] = arrhythmia_diagnosis.get('PVCs', '')
                    summary_row['pacs_count'] = arrhythmia_diagnosis.get('PACs', '')
                    summary_row['rr_interval'] = arrhythmia_diagnosis.get('RRInterval', '')
                else:
                    summary_row['rhythm_type'] = ''
                    summary_row['heart_rate'] = ''
                    summary_row['sna_feature'] = ''
                    summary_row['pvcs_count'] = ''
                    summary_row['pacs_count'] = ''
                    summary_row['rr_interval'] = ''

                # 健康指标
                health_metrics = result.get('health_metrics', {})
                if isinstance(health_metrics, dict):
                    summary_row['health_score'] = health_metrics.get('HealthScore', '')
                    summary_row['stress_index'] = health_metrics.get('StressIndex', '')
                    summary_row['fatigue_index'] = health_metrics.get('FatigueIndex', '')
                    summary_row['autonomic_balance'] = health_metrics.get('AutonomicBalance', '')
                else:
                    summary_row['health_score'] = ''
                    summary_row['stress_index'] = ''
                    summary_row['fatigue_index'] = ''
                    summary_row['autonomic_balance'] = ''

                # PQRSTC波形参数
                pqrstc = result.get('pqrstc', {})
                if isinstance(pqrstc, dict):
                    summary_row['p_wave'] = pqrstc.get('P', '')
                    summary_row['pr_interval'] = pqrstc.get('PR', '')
                    summary_row['qrs_duration'] = pqrstc.get('QRS', '')
                    summary_row['qt_interval'] = pqrstc.get('QT', '')
                    summary_row['qtc_interval'] = pqrstc.get('QTc', '')
                    summary_row['t_wave'] = pqrstc.get('T', '')
                else:
                    summary_row['p_wave'] = ''
                    summary_row['pr_interval'] = ''
                    summary_row['qrs_duration'] = ''
                    summary_row['qt_interval'] = ''
                    summary_row['qtc_interval'] = ''
                    summary_row['t_wave'] = ''

                summary_data.append(summary_row)

            summary_df = pd.DataFrame(summary_data)
            summary_file = os.path.join(output_dir, f"batch_diagnosis_summary_{int(time.time())}.csv")
            summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
            logging.info(f"汇总结果已保存到: {summary_file}")

        except Exception as e:
            logging.error(f"保存批量结果时出错: {e}")

    return results


# def timing_decorator(func): # Commented out timing_decorator
#     """性能监控装饰器"""
#
#     @wraps(func)
#     def wrapper(*args, **kwargs):
#         start_time = time.time()
#         result = func(*args, **kwargs)
#         end_time = time.time()
#         logging.info(f"{func.__name__} took {end_time - start_time:.2f} seconds")
#         return result
#
#     return wrapper


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='ECG Analysis Tool')
    parser.add_argument('--mode', type=int, choices=[1, 2, 3, 4, 5], help='处理模式：1=单文件，2=文件夹，3=七牛云es_key诊断，4=批量es_key诊断，5=Excel文件es_key诊断')
    parser.add_argument('--input', type=str, help='输入文件或文件夹路径，或es_key，或包含es_key的文件')
    parser.add_argument('--output', type=str, help='输出文件夹路径')
    parser.add_argument('--debug', action='store_true', help='启用调试模式，输出更详细的日志')
    parser.add_argument('--sample-rate', type=int, default=500, help='采样率，默认为500Hz')
    parser.add_argument('--environment', type=str, choices=['test', 'prod'], default='prod', help='七牛云环境：test或prod，默认为prod')
    parser.add_argument('--es-keys', type=str, nargs='+', help='多个es_key，用空格分隔')
    return parser.parse_args()


# 在文件顶部导入部分添加


# 在 get_token 函数开始处添加以下代码
def get_token(force_refresh=False, refresh_token=False):
    """获取API的认证令牌，支持缓存和自动刷新

    Parameters:
        force_refresh (bool): 是否强制刷新获取新token
        refresh_token (bool): 如果为True，仅刷新token的过期时间，不重新获取
    """
    global token_value, token_obtain_time, session

    current_time = time.time()

    # 如果仅刷新token的过期时间
    if refresh_token and token_value:
        token_obtain_time = current_time
        logging.info(f"刷新token的过期时间，token: {token_value[:10]}...")
        return token_value

    # 如果token存在且未过期且不强制刷新，直接返回
    if not force_refresh and token_value and (current_time - token_obtain_time) < TOKEN_REFRESH_THRESHOLD:
        logging.info(
            f"使用缓存的token: {token_value[:10]}... (还有 {TOKEN_REFRESH_THRESHOLD - (current_time - token_obtain_time):.1f} 秒过期)")
        return token_value

    try:
        # 直接使用本地URL，而不是从Configuration文件读取
        # login_url = 'http://minute.ecggpt.test.aiweihe.com/api/login/'  # 测试环境地址
        # login_url = 'http://minute.ecggpt.test.aiweihe.com/api/login/'
        #login_url = 'http://ecggpt.aiweihe.com/api/login/'
        # login_url = 'http://test.ecggpt.aiweihe.com/api/login/'
        login_url = 'http://127.0.0.1:8000/api/login/'  # 修改为正式环境地址

        # 直接使用硬编码的客户端ID和密钥，不再从配置文件获取
        client_id = "znipr4p6"  # 使用日志中显示的客户端ID
        client_secret = "383cd1d7325046029af2d2f984395103"  # 硬编码的备用密钥

        params = {
            "clientId": client_id,
            "clientSecret": client_secret
        }

        logging.info(f"尝试获取token，URL: {login_url}")
        logging.info(f"认证参数: clientId={client_id}")

        # 重试机制
        max_retries = 3
        retry_count = 0
        retry_delay = 1

        while retry_count < max_retries:
            try:
                # 重置会话，避免之前的连接问题
                if retry_count > 0:
                    session = requests.Session()
                    adapter = requests.adapters.HTTPAdapter(
                        pool_connections=100,
                        pool_maxsize=100,
                        max_retries=3
                    )
                    session.mount('http://', adapter)
                    session.mount('https://', adapter)
                    logging.info(f"重试获取token (第{retry_count}次)")

                headers = {'Content-Type': 'application/x-www-form-urlencoded'}
                resp = session.post(login_url, data=params, headers=headers, timeout=10)
                logging.info(f"Token请求状态码: {resp.status_code}")

                resp.raise_for_status()
                resp_json = resp.json()

                if 'data' in resp_json and 'token' in resp_json['data']:
                    token_value = resp_json['data']['token']
                    token_obtain_time = current_time
                    logging.info(f"成功获取token: {token_value[:10]}...")
                    return token_value
                else:
                    logging.error(f"Token响应格式错误: {resp_json}")
                    retry_count += 1
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避

            except requests.exceptions.RequestException as e:
                logging.error(f"Token请求错误: {e}")
                retry_count += 1
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避

        logging.error("获取token失败，已达到最大重试次数")
        return None

    except Exception as e:
        logging.error(f"获取token时发生错误: {e}")
        return None


# @timing_decorator # Commented out
def profile_api_call(ecg_data, fs, token):
    """对单次API调用进行性能分析"""
    # profiler = cProfile.Profile()
    # profiler.enable()

    result, sna_features, raw_response = ecg_analysis(ecg_data, fs, token)

    # profiler.disable()

    # 创建字符串流来捕获输出
    # s = io.StringIO()
    # stats = pstats.Stats(profiler, stream=s).sort_stats(SortKey.TIME)
    # stats.print_stats(10)  # 只打印前10个最耗时的函数

    # logging.info("Performance Analysis:\\n" + s.getvalue())
    return result, sna_features, raw_response


# @timing_decorator # Commented out
def ecg_analysis(ecg_data, fs, token, retry_count=0):
    """调用心电分析API，支持自动重试和token刷新"""
    global session, token_value

    logging.debug(f"DEBUG: ecg_analysis called with fs={fs}")
    logging.debug(f"DEBUG: Original ecg_data type: {type(ecg_data)}, length: {len(ecg_data)}")
    if ecg_data and len(ecg_data) > 10:
        logging.debug(f"DEBUG: Original ecg_data samples (first 5): {ecg_data[:5]}, (last 5): {ecg_data[-5:]}")
    else:
        logging.debug(f"DEBUG: Original ecg_data: {ecg_data}")
    logging.debug(f"DEBUG: Token used: {token[:10]}...")

    # 增加请求间隔，避免触发API限制
    # 对于重试次数，增加更长的等待时间
    wait_time = REQUEST_INTERVAL * (retry_count + 1)
    logging.info(f"等待 {wait_time} 秒后发送请求...")
    time.sleep(wait_time)

    # 检查token是否需要刷新
    current_time = time.time()
    if current_time - token_obtain_time > TOKEN_REFRESH_THRESHOLD:
        logging.info("Token即将过期，尝试刷新")
        new_token = get_token(force_refresh=True)
        if new_token:
            token = new_token
            token_value = new_token

    # 使用本地地址测试本地功能
    #analysis_url = 'http://minute.ecggpt.test.aiweihe.com/api/diagnose/arrhythmia/'
    #analysis_url = 'http://ecggpt.aiweihe.com/api/diagnose/arrhythmia/'
    #analysis_url = 'http://test.ecggpt.aiweihe.com/api/diagnose/arrhythmia/'
    analysis_url = 'http://127.0.0.1:8000/api/diagnose/arrhythmia/'
    headers = {
        "Content-Type": "application/json",
        "X-Auth-Token": token
    }

    # 数据统计信息
    data_stats = {
        "min": min(ecg_data) if ecg_data else None,
        "max": max(ecg_data) if ecg_data else None,
        "mean": sum(ecg_data) / len(ecg_data) if ecg_data else None,
        "is_list": isinstance(ecg_data, list),
        "length": len(ecg_data) if hasattr(ecg_data, "__len__") else "N/A",
        "sample_first_5": str(ecg_data[:5]) if hasattr(ecg_data, "__getitem__") and len(ecg_data) >= 5 else "N/A"
    }
    logging.debug(f"ECG数据统计: {data_stats}")

    # 确保所有数据都是合法的数字（非NaN、非无穷大）
    cleaned_ecg_data = []
    for value in ecg_data:
        if isinstance(value, (int, float)) and not pd.isna(value) and pd.notna(value):
            # 检查是否为无穷大值
            if abs(value) != float('inf'):
                cleaned_ecg_data.append(value)
            else:
                cleaned_ecg_data.append(0)  # 替换无穷大值为0
        else:
            cleaned_ecg_data.append(0)  # 替换非数值为0

    # 记录清理后的数据信息
    logging.debug(f"清理后的数据长度: {len(cleaned_ecg_data)}")
    if len(cleaned_ecg_data) != len(ecg_data):
        logging.warning(f"数据清理过程中移除了 {len(ecg_data) - len(cleaned_ecg_data)} 个无效值")

    logging.debug(f"DEBUG: Cleaned ecg_data type: {type(cleaned_ecg_data)}, length: {len(cleaned_ecg_data)}")
    if cleaned_ecg_data and len(cleaned_ecg_data) > 10:
        logging.debug(
            f"DEBUG: Cleaned ecg_data samples (first 5): {cleaned_ecg_data[:5]}, (last 5): {cleaned_ecg_data[-5:]}")
    else:
        logging.debug(f"DEBUG: Cleaned ecg_data: {cleaned_ecg_data}")

    # 使用JSON序列化，确保数据被正确格式化为JSON数组
    params = {
        "signal": cleaned_ecg_data,  # 直接传递列表，不转字符串
        "fs": int(fs),  # 确保fs是整数
        "adc_gain": 1,
        "adc_zero": 0,
        'union_id': 'test_user',
        'ecg_age_key': 1,  # 修改为1，启用心脏年龄计算
        'emotion_key': 1,  # 修改为1，启用情绪计算
        'health_metrics': 1  # 添加API需要的health_metrics参数
    }

    params_for_logging = params.copy()
    if 'signal' in params_for_logging:
        s_data = params_for_logging['signal']
        if s_data and len(s_data) > 10:
            params_for_logging['signal'] = f"list_len_{len(s_data)}_samples_({s_data[:5]}...{s_data[-5:]})"
        else:
            params_for_logging['signal'] = f"list_len_{len(s_data)}_data_({s_data})"
    logging.debug(f"DEBUG: API request params: {json.dumps(params_for_logging, ensure_ascii=False)}")

    try:
        logging.info("发送API请求...")
        logging.info(f"数据长度: {len(cleaned_ecg_data)}")
        logging.info(f"采样率: {fs}")
        logging.info(f"API地址: {analysis_url}")
        logging.info(f"使用token: {token[:10]}...")  # 只显示token的前10个字符

        # 设置超时，避免请求卡住
        # 将超时时间从30秒增加到120秒或更长
        resp = session.post(analysis_url, headers=headers, json=params, timeout=120)
        logging.debug(f"DEBUG: API response status code: {resp.status_code}")

        if resp.status_code == 401:
            # 401可能是token过期，尝试刷新token并重试
            if retry_count < 2:  # 最多重试2次
                logging.warning("Token可能已过期，尝试刷新并重试")
                new_token = get_token(force_refresh=True)
                if new_token:
                    return ecg_analysis(ecg_data, fs, new_token, retry_count + 1)
            logging.error("Token刷新后仍然失败")
            return None, None, None
        elif resp.status_code != 200:
            # 其他错误状态码
            logging.error(f"API响应错误: {resp.status_code}")

            # 如果是服务器错误，尝试重试
            if resp.status_code >= 500 and retry_count < 3:
                retry_delay = 1 * (2 ** retry_count)  # 指数退避
                logging.warning(f"服务器错误，{retry_delay}秒后重试 (第{retry_count + 1}次)")
                time.sleep(retry_delay)
                return ecg_analysis(ecg_data, fs, token, retry_count + 1)

            return None, None, None

        # 获取完整的原始API响应
        resp_json = resp.json()
        raw_response = resp_json  # 保存原始响应

        # 增加：记录完整的API响应，包括详细的错误信息
        if resp_json.get("code") != 0:
            logging.debug("DEBUG: ---- API Raw Error Response Start ----")
            logging.info(f"API详细错误响应: {json.dumps(resp_json, ensure_ascii=False, indent=2)}")
            logging.debug("DEBUG: ---- API Raw Error Response End ----")

        # 检查是否是错误代码4（token过期）
        if resp_json.get("code") == 4:
            if retry_count < 2:  # 最多重试2次
                logging.warning("Token已过期（错误代码4），尝试刷新token并重试")
                new_token = get_token(force_refresh=True)
                if new_token:
                    return ecg_analysis(ecg_data, fs, new_token, retry_count + 1)
            logging.error(f"Token刷新后仍然失败，详细错误信息: {resp_json}")
            return None, None, None

        if resp_json.get("code") == 0:
            # API调用成功，刷新token的过期时间
            get_token(refresh_token=True)
            logging.info("API调用成功，已刷新token的过期时间")

            data = resp_json.get("data", {})
            sna_feature = data.get('ArrhythmiaDiagnosis', {}).get('SNA', False)
            sna_features = data.get('SNA_Features', {})

            # 移除完整API响应输出代码

            # 只输出关键诊断信息
            arrhythmia = data.get('ArrhythmiaDiagnosis', {})
            positive_diagnoses = [k for k, v in arrhythmia.items() if v == 1]
            if positive_diagnoses:
                logging.info(f"诊断结果: {', '.join(positive_diagnoses)}")

            metrics = data.get('HealthMetrics', {})
            logging.info(f"健康指标: 压力={metrics.get('Pressure', 0)}, "
                         f"HRV={metrics.get('HRV', 0)}, "
                         f"疲劳={metrics.get('Fatigue', 0)}, "
                         f"活力={metrics.get('Vitality', 0)}")

            pqrstc = data.get('PQRSTC', {})
            logging.info(
                f"心率: {pqrstc.get('HR', 0)} bpm, QRS: {pqrstc.get('QRS_duration', pqrstc.get('QRS_QRSDuration', 0))} ms, "
                f"QT: {pqrstc.get('QT', 0)} ms, QTc: {pqrstc.get('QTc', 0)} ms")

            # Signal quality信息
            is_noise = data.get('IsNoise', False)
            noise_message_from_api = data.get('NoiseMessage', 'N/A')
            signal_quantity_from_api = data.get('SignalQuantity', 'N/A')
            # 为了日志更清晰，获取API原始返回的IsNoise值，如果不存在则标记
            is_noise_raw_from_api = data.get('IsNoise')
            if is_noise_raw_from_api is None:
                is_noise_log_val = 'N/A (defaulted to False in script)'
            else:
                is_noise_log_val = is_noise_raw_from_api

            logging.info(
                f"信号状态: {'噪音信号' if is_noise else '正常信号'} "
                f"(API返回 - IsNoise: {is_noise_log_val}, "
                f"NoiseMessage: '{noise_message_from_api}', "
                f"SignalQuantity: {signal_quantity_from_api})"
            )

            # 返回数据、SNA特征和原始API响应
            return data, sna_features, raw_response
        else:
            error_code = resp_json.get("code")
            error_message = resp_json.get("msg")
            detailed_error_message = ERROR_CODE_MAP.get(error_code, error_message)
            logging.error(f"API返回错误: {detailed_error_message} (code: {error_code})")

            # 如果是token相关错误，尝试刷新token
            if error_code in [401, 403, 4] and retry_count < 2:
                logging.warning("可能是token过期，尝试刷新token并重试")
                new_token = get_token(force_refresh=True)
                if new_token:
                    return ecg_analysis(ecg_data, fs, new_token, retry_count + 1)
            return None, None, resp_json  # 返回原始错误响应而不是None

    except requests.exceptions.RequestException as e:
        logging.error(f"API请求错误: {e}")

        # 对于连接超时错误，尝试重试
        if isinstance(e, (requests.exceptions.ConnectionError, requests.exceptions.Timeout)) and retry_count < 3:
            retry_delay = 1 * (2 ** retry_count)  # 指数退避
            logging.warning(f"连接错误，{retry_delay}秒后重试 (第{retry_count + 1}次)")
            time.sleep(retry_delay)

            # 重置会话
            session = requests.Session()
            adapter = requests.adapters.HTTPAdapter(
                pool_connections=100,
                pool_maxsize=100,
                max_retries=3
            )
            session.mount('http://', adapter)
            session.mount('https://', adapter)

            return ecg_analysis(ecg_data, fs, token, retry_count + 1)
        
        # 创建一个包含错误信息的响应对象
        error_response = {
            "code": -1,  # 自定义错误代码
            "msg": f"API请求错误: {str(e)}",  # 错误消息
            "data": None
        }
        return None, None, error_response


def get_disease_name(arrhythmia_diagnosis):
    """将识别到的疾病转换为文字"""
    disease_mapping = {
        'SN': '窦性心律',
        'SNA': '窦性心律不齐',
        'SNT': '窦性心动过速',
        'SNB': '窦性心动过缓',
        'PVC': '室性早搏',
        'PSC': '不确定的早搏类型',
        'PJC': '交界性早搏',
        'PAC': '房性早搏',
        'VT': '室性心动过速',
        'SVT': '室上性心动过速',
        'AFL': '心房扑动',
        'AF': '心房颤动',
        'WPW': 'WPW综合征',
        'VE': '室性逸搏',
        'JE': '交界性逸搏',
        'AE': '房性逸搏',
        'AVBI': '一度房室传导阻滞',
        'AVBII': '二度房室传导阻滞',
        'AVBIII': '三度房室传导阻滞',
        'IVB': '室内传导阻滞',
        'LBBB': '左束支传导阻滞',
        'RBBB': '右束支传导阻滞',
        'LAFB': '左前分支传导阻滞',
        'BRU': 'Brugada综合征',
        'LQT': 'QT间期延长',
        'bPVC': '成对室早',
        # 添加其他可能的多结论诊断映射
        'ISC': '心肌缺血',
        'LVH': '左心室肥大',
        'RVH': '右心室肥大',
        'LAH': '左心房肥大',
        'RAH': '右心房肥大',
        'MI': '心肌梗死'
    }
    # 新增逻辑：如果字典为空或所有值都为0，直接返回"Sinus Rhythm"
    if not arrhythmia_diagnosis or all(v == 0 for v in arrhythmia_diagnosis.values()):
        return '窦性心律'
    diagnoses = [k for k, v in arrhythmia_diagnosis.items() if v == 1]
    chinese_diagnoses = [disease_mapping.get(diagnosis, diagnosis) for diagnosis in diagnoses]
    return ', '.join(chinese_diagnoses)  # 返回逗号分隔的字符串


# 添加缺失的 get_multi_label_disease_name 函数定义
def get_multi_label_disease_name(multi_label_diagnosis_list):
    """将多结论诊断列表转换为文字列表"""
    disease_mapping = {
        'SN': '窦性心律',
        'SNA': '窦性心律不齐',
        'SNT': '窦性心动过速',
        'SNB': '窦性心动过缓',
        'PVC': '室性早搏',
        'PSC': '不确定的早搏类型',
        'PJC': '交界性早搏',
        'PAC': '房性早搏',
        'VT': '室性心动过速',
        'SVT': '室上性心动过速',
        'AFL': '心房扑动',
        'AF': '心房颤动',
        'WPW': 'WPW综合征',
        'VE': '室性逸搏',
        'JE': '交界性逸搏',
        'AE': '房性逸搏',
        'AVBI': '一度房室传导阻滞',
        'AVBII': '二度房室传导阻滞',
        'AVBIII': '三度房室传导阻滞',
        'IVB': '室内传导阻滞',
        'LBBB': '左束支传导阻滞',
        'RBBB': '右束支传导阻滞',
        'LAFB': '左前分支传导阻滞',
        'BRU': 'Brugada综合征',
        'LQT': 'QT间期延长',
        'bPVC': '成对室早',
        # 添加其他可能的多结论诊断映射
        'ISC': '心肌缺血',
        'LVH': '左心室肥大',
        'RVH': '右心室肥大',
        'LAH': '左心房肥大',
        'RAH': '右心房肥大',
        'MI': '心肌梗死'
    }
    # 遍历传入的列表 multi_label_diagnosis_list
    chinese_diagnoses = [disease_mapping.get(diagnosis, diagnosis) for diagnosis in multi_label_diagnosis_list]
    return chinese_diagnoses  # 返回中文列表


# @timing_decorator
def process_single_file(input_file, sampling_rate, token):
    """处理单个文件的函数 - 单个处理模式"""
    # 使用全局token变量
    global token_value

    # 强制使用500HzSampling rate
    # sampling_rate = 500 # 用户要求使用传入的Sampling rate
    logging.info(f"使用传入的采样率: {sampling_rate}Hz")

    results_list = []
    # 定义输出列，包含原始API响应列
    cols = [
        'Row', 'ID', 'Status', 'First10sSkipped', 'Diseases', 'MultiLabelDiagnosis', 'ECGAge',
        'ArrhythmiaDiagnosis', 'PQRSTC', 'HealthMetrics',
        'SignalQuantity', 'IsNoise', 'NoiseMessage',
        'RawResponse',  # 保存完整原始API响应
        'ErrorMessage'
    ]

    file_extension = os.path.splitext(input_file)[1].lower()

    if file_extension in ['.h5', '.hdf5']:
        logging.info(f"开始处理HDF5文件: {input_file}")
        detailed_error_count = 0  # Initialize counter
        try:
            h5_file = h5py.File(input_file, 'r')
            if 'ecg_data' not in h5_file:
                logging.error(f"HDF5 文件 {input_file} 中未找到 'ecg_data' 数据集")
                if 'h5_file' in locals() and h5_file:  # Ensure file is closed if opened
                    h5_file.close()
                return pd.DataFrame(columns=cols)

            dset = h5_file['ecg_data']
            num_rows = dset.shape[0]
            logging.info(f"HDF5文件 '{input_file}' 中的 'ecg_data' 数据集包含 {num_rows} 行数据.")

            # 尝试获取H5文件的字段名列表
            field_names = []
            if hasattr(dset, 'dtype') and hasattr(dset.dtype, 'names') and dset.dtype.names:
                field_names = list(dset.dtype.names)
                logging.info(f"HDF5文件中的字段: {', '.join(field_names)}")
            else:
                logging.warning("无法获取HDF5文件的字段结构")

            # 检查是否有id字段
            has_id_field = 'id' in field_names
            if has_id_field:
                logging.info("检测到id字段，将提取并添加到结果中")
            else:
                logging.warning("未检测到id字段，将使用行号作为ID")

            # 确定使用哪个字段作为心电数据源
            ecg_field = None
            for field_candidate in ['ecgII', 'ecgI', 'ecg', 'signal']:
                if field_candidate in field_names:
                    ecg_field = field_candidate
                    logging.info(f"将使用字段 '{ecg_field}' 作为心电数据源")
                    break

            if not ecg_field and field_names:
                # 如果没有找到预期的字段但有其他字段，使用第一个可用字段
                ecg_field = field_names[0]
                logging.warning(f"未找到预期的心电数据字段，将使用第一个可用字段 '{ecg_field}'")
            elif not ecg_field:
                logging.error("无法确定心电数据字段，HDF5文件可能格式不正确")
                if 'h5_file' in locals() and h5_file:
                    h5_file.close()
                return pd.DataFrame(columns=cols)

            for i in range(num_rows):
                row_label = f"HDF5 文件 '{os.path.basename(input_file)}', 行 {i + 1}"
                try:
                    h5_row_data = dset[i]

                    # 提取ID字段（如果存在）
                    record_id = None
                    if has_id_field:
                        record_id = h5_row_data['id']
                        if isinstance(record_id, bytes):
                            try:
                                record_id = record_id.decode('utf-8')
                            except UnicodeDecodeError:
                                record_id = str(record_id)
                        logging.info(f"{row_label}: 提取到ID: {record_id}")
                    else:
                        record_id = f"行{i + 1}"

                    # 检查字段是否存在于当前行
                    if ecg_field not in h5_row_data.dtype.names:
                        logging.warning(
                            f"{row_label}: 数据行中未找到 '{ecg_field}' 字段。可用的字段: {h5_row_data.dtype.names}")
                        error_row = {col: None for col in cols}
                        error_row.update({
                            'Row': i + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': f"数据行中未找到 '{ecg_field}' 字段",
                            'First10sSkipped': None
                        })
                        results_list.append(error_row)
                        continue

                    ecg_data_raw = h5_row_data[ecg_field]
                    ecg_data_temp = []

                    # 针对HDF5文件中的bytes类型ECG数据进行处理
                    if isinstance(ecg_data_raw, bytes):
                        try:
                            # 将bytes解码为字符串
                            ecg_str = ecg_data_raw.decode('utf-8').strip()

                            # 尝试判断字符串格式：JSON数组、CSV格式或其他
                            if (ecg_str.startswith('[') and ecg_str.endswith(']')) or ',' in ecg_str:
                                # 看起来是JSON数组或CSV格式的字符串

                                # 移除方括号（如果有）
                                if ecg_str.startswith('[') and ecg_str.endswith(']'):
                                    ecg_str = ecg_str[1:-1]

                                # 分割字符串并尝试转换为浮点数
                                items = ecg_str.split(',')
                                for item in items:
                                    item = item.strip()
                                    if item:  # 确保不是空字符串
                                        try:
                                            value = float(item)
                                            ecg_data_temp.append(value)
                                        except ValueError:
                                            # 如果单个值转换失败，记录错误并跳过该值
                                            if detailed_error_count < 3:
                                                logging.warning(
                                                    f"DIAGNOSTIC ({row_label}): 无法将 '{item}' 转换为浮点数，已跳过")
                                                detailed_error_count += 1
                            else:
                                # 尝试作为单个数值处理
                                try:
                                    value = float(ecg_str)
                                    ecg_data_temp.append(value)
                                except ValueError:
                                    if detailed_error_count < 3:
                                        logging.error(
                                            f"DIAGNOSTIC ({row_label}): 无法将字符串 '{ecg_str[:100]}...' 转换为数值")
                                        detailed_error_count += 1

                            if ecg_data_temp:
                                logging.info(
                                    f"DIAGNOSTIC ({row_label}): 成功从bytes解析数据，获取 {len(ecg_data_temp)} 个数值")

                        except (UnicodeDecodeError, ValueError) as e:
                            # 如果处理失败，记录错误并设置为空列表
                            if detailed_error_count < 5:
                                logging.error(
                                    f"DIAGNOSTIC ({row_label}): bytes处理失败: {str(e)}, 内容: {str(ecg_data_raw)[:100]}...")
                                detailed_error_count += 1
                    # 处理字符串类型
                    elif isinstance(ecg_data_raw, str):
                        try:
                            ecg_str = ecg_data_raw.strip()

                            # 同样判断字符串格式
                            if (ecg_str.startswith('[') and ecg_str.endswith(']')) or ',' in ecg_str:
                                # 移除方括号（如果有）
                                if ecg_str.startswith('[') and ecg_str.endswith(']'):
                                    ecg_str = ecg_str[1:-1]

                                # 分割字符串并尝试转换为浮点数
                                items = ecg_str.split(',')
                                for item in items:
                                    item = item.strip()
                                    if item:  # 确保不是空字符串
                                        try:
                                            value = float(item)
                                            ecg_data_temp.append(value)
                                        except ValueError:
                                            if detailed_error_count < 3:
                                                logging.warning(
                                                    f"DIAGNOSTIC ({row_label}): 无法将 '{item}' 转换为浮点数，已跳过")
                                                detailed_error_count += 1
                            else:
                                # 尝试作为单个数值处理
                                try:
                                    value = float(ecg_str)
                                    ecg_data_temp.append(value)
                                except ValueError:
                                    if detailed_error_count < 3:
                                        logging.error(
                                            f"DIAGNOSTIC ({row_label}): 无法将字符串 '{ecg_str[:100]}...' 转换为数值")
                                        detailed_error_count += 1
                        except ValueError as e:
                            # 如果转换失败，记录更多信息
                            if detailed_error_count < 5:
                                logging.info(
                                    f"DIAGNOSTIC ({row_label}): 字符串转换失败: {str(e)}, 字符串内容: {ecg_data_raw[:100]}...")
                                detailed_error_count += 1
                    # 处理NumPy数组或可转为列表的对象
                    elif hasattr(ecg_data_raw, 'tolist'):
                        ecg_data_temp = ecg_data_raw.tolist()
                    # 处理已经是列表或元组的数据
                    elif isinstance(ecg_data_raw, (list, tuple)):
                        ecg_data_temp = list(ecg_data_raw)
                    else:  # 其他类型
                        # 尝试直接转为数值
                        try:
                            ecg_data_temp = [float(ecg_data_raw)]
                        except (ValueError, TypeError):
                            if detailed_error_count < 3:
                                logging.error(f"DIAGNOSTIC ({row_label}): 无法处理类型为 {type(ecg_data_raw)} 的数据")
                                detailed_error_count += 1
                            ecg_data_temp = []

                    # 最终处理：使用pandas函数确保所有值都是数值类型
                    if ecg_data_temp:
                        ecg_data_after_numeric = pd.to_numeric(ecg_data_temp, errors='coerce').tolist()
                        ecg_data = [x for x in ecg_data_after_numeric if pd.notna(x)]
                    else:
                        ecg_data = []

                    if not ecg_data:
                        if detailed_error_count < 5:  # Log details for the first 5 such errors
                            logging.info(f"DIAGNOSTIC ({row_label}): ---- Error Details Start ----")
                            logging.info(
                                f"DIAGNOSTIC ({row_label}): ecg_data_raw (type: {type(ecg_data_raw)}): {repr(ecg_data_raw)}")
                            if hasattr(ecg_data_raw, 'shape'):
                                logging.info(f"DIAGNOSTIC ({row_label}): ecg_data_raw.shape: {ecg_data_raw.shape}")
                            if hasattr(ecg_data_raw, 'dtype'):
                                logging.info(f"DIAGNOSTIC ({row_label}): ecg_data_raw.dtype: {ecg_data_raw.dtype}")
                            # Log ecg_data_temp which was the direct input to pd.to_numeric
                            logging.info(
                                f"DIAGNOSTIC ({row_label}): ecg_data_temp (input to pd.to_numeric, type: {type(ecg_data_temp)}): {repr(ecg_data_temp)}")
                            if ecg_data_after_numeric:
                                logging.info(
                                    f"DIAGNOSTIC ({row_label}): ecg_data_after_numeric (after pd.to_numeric, before dropna, type: {type(ecg_data_after_numeric)}): {repr(ecg_data_after_numeric)}")
                            logging.info(f"DIAGNOSTIC ({row_label}): ---- Error Details End ----")
                            detailed_error_count += 1

                        logging.warning(f"{row_label}: '{ecg_field}' 数据为空或所有值都无法转换为有效数值")
                        error_row = {col: None for col in cols}
                        error_row.update({
                            'Row': i + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': f"'{ecg_field}' 数据为空或无效",
                            'First10sSkipped': None
                        })
                        results_list.append(error_row)
                        continue

                    min_required_samples = sampling_rate * 10
                    if len(ecg_data) < min_required_samples:
                        logging.warning(
                            f"{row_label}: 数据长度不足10秒 ({len(ecg_data)} < {min_required_samples})，跳过")
                        error_row = {col: None for col in cols}
                        error_row.update({
                            'Row': i + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': f'数据长度不足10秒({len(ecg_data)}<{min_required_samples})',
                            'First10sSkipped': None
                        })
                        results_list.append(error_row)
                        continue

                    ecg_signal = ecg_data
                    logging.info(f"{row_label}: 准备发送 {len(ecg_signal)} 个样本点给 API")

                    # 使用最新的token_value进行API调用
                    current_token = token_value or token

                    # 调用API分析数据
                    result, _, raw_response = profile_api_call(ecg_signal, sampling_rate, current_token)

                    if result:
                        arrhythmia_diagnosis = result.get('ArrhythmiaDiagnosis', {})
                        diseases = get_disease_name(arrhythmia_diagnosis)
                        multi_label_diagnosis = result.get('MultiLabelDiagnosis', [])
                        chinese_multi_label = get_multi_label_disease_name(multi_label_diagnosis)
                        signal_quantity = result.get('SignalQuantity')
                        is_noise = result.get('IsNoise', False)
                        noise_message = result.get('NoiseMessage', '')
                        first_10s_skipped = noise_message.startswith("前10秒信号可能为噪音")

                        # Log details (similar to CSV path)
                        logging.info(f"{row_label} 诊断结果:")
                        logging.info(f"  传统诊断结果: {diseases}")
                        logging.info(
                            f"  多结论诊断结果: {', '.join(chinese_multi_label) if chinese_multi_label else '无'}")
                        logging.info(f"  信号质量: {signal_quantity}, 是否为噪音: {'是' if is_noise else '否'}")
                        
                        # 移除原始响应中的ecgAnalysis字段输出代码

                        new_row = {
                            'Row': i + 1,
                            'ID': record_id,
                            'ECGAge': result.get('ECGAge'),
                            'ArrhythmiaDiagnosis': result.get('ArrhythmiaDiagnosis', {}),
                            'MultiLabelDiagnosis': chinese_multi_label,
                            'HealthMetrics': result.get('HealthMetrics', {}),
                            'PQRSTC': result.get('PQRSTC', {}),
                            'SignalQuantity': signal_quantity,
                            'IsNoise': is_noise,
                            'NoiseMessage': noise_message,
                            'Status': '成功',
                            'Diseases': diseases,
                            'First10sSkipped': first_10s_skipped,
                            'RawResponse': raw_response,  # 保存完整原始API响应
                            'ErrorMessage': ''
                        }
                    else:
                        # 获取API错误详细信息
                        error_info = ""
                        if raw_response:
                            error_code = raw_response.get("code")
                            error_msg = raw_response.get("msg", "")
                            error_info = f"API返回错误: 代码={error_code}, 消息={error_msg}"
                        
                        logging.warning(f"{row_label}: API处理失败 - {error_info}")
                        new_row = {col: None for col in cols}
                        new_row.update({
                            'Row': i + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': error_info or 'API调用失败或返回错误',
                            'First10sSkipped': None,
                            'RawResponse': raw_response  # 保存原始错误响应
                        })
                    results_list.append(new_row)

                except Exception as e:
                    logging.error(f"{row_label}: 处理时发生意外错误: {str(e)}")
                    error_row = {col: None for col in cols}
                    error_row.update({
                        'Row': i + 1,
                        'ID': has_id_field and h5_row_data.get('id', f"行{i + 1}") or f"行{i + 1}",
                        'Status': '失败',
                        'ErrorMessage': f'处理错误: {str(e)}',
                        'First10sSkipped': None
                    })
                    results_list.append(error_row)

            if 'h5_file' in locals() and h5_file:  # Ensure file is closed
                h5_file.close()
                logging.info(f"已关闭HDF5文件: {input_file}")

        except FileNotFoundError:
            logging.error(f"HDF5输入文件未找到: {input_file}")
            return pd.DataFrame(columns=cols)
        except Exception as e:
            logging.error(f"读取或处理HDF5文件 {input_file} 时发生严重错误: {str(e)}")
            if 'h5_file' in locals() and h5_file:  # Ensure file is closed in case of error after opening
                h5_file.close()
            return pd.DataFrame(columns=cols)

    elif file_extension == '.csv':
        logging.info(f"开始处理CSV文件: {input_file}")
        chunk_size = 1000  # Specific to CSV processing
        try:
            # 使用 chunksize 优化大文件内存使用
            for chunk_idx, chunk in enumerate(
                    pd.read_csv(input_file, encoding='utf-8', header=None, chunksize=chunk_size)):
                logging.info(f"处理文件块 {chunk_idx + 1}...")
                for idx, row in chunk.iterrows():
                    # Construct a unique row label for logging, incorporating chunk index and original row index
                    # The original 'idx' is 0-based within the chunk.
                    # To get a global row index, we need: chunk_idx * chunk_size + idx (if idx is 0-based for chunk)
                    # However, for user display, 'Row {idx + 1}' within the chunk is often simpler if chunk_size is large.
                    # Let's use a slightly more descriptive label that matches the HDF5 one for consistency.
                    original_row_index_in_file = chunk_idx * chunk_size + idx
                    row_label = f"CSV 文件 '{os.path.basename(input_file)}', 行 {original_row_index_in_file + 1}"

                    # 使用行号作为CSV文件的ID
                    record_id = f"行{original_row_index_in_file + 1}"

                    try:
                        # 数据预处理
                        ecg_data = pd.to_numeric(row, errors='coerce').dropna().tolist()
                        if not ecg_data:
                            logging.warning(f"{row_label}: 数据行为空或无法转换为数值")
                            error_row = {col: None for col in cols}  # 初始化所有列为None
                            error_row.update({
                                'Row': original_row_index_in_file + 1,  # Use original_row_index_in_file
                                'ID': record_id,
                                'Status': '失败',
                                'ErrorMessage': '数据行为空或无效',
                                'First10sSkipped': None
                            })
                            results_list.append(error_row)
                            continue

                        # 添加基础长度检查 (至少需要10秒数据)
                        min_required_samples = sampling_rate * 10
                        if len(ecg_data) < min_required_samples:
                            logging.warning(
                                f"{row_label}: 数据长度不足10秒 ({len(ecg_data)} < {min_required_samples})，跳过")
                            error_row = {col: None for col in cols}
                            error_row.update({
                                'Row': original_row_index_in_file + 1,  # Use original_row_index_in_file
                                'ID': record_id,
                                'Status': '失败',
                                'ErrorMessage': f'数据长度不足10秒({len(ecg_data)}<{min_required_samples})',
                                'First10sSkipped': None
                            })
                            results_list.append(error_row)
                            continue

                        # 直接使用整行数据作为信号
                        ecg_signal = ecg_data
                        logging.info(f"{row_label}: 准备发送 {len(ecg_signal)} 个样本点给 API")

                        # 使用最新的token_value进行API调用
                        current_token = token_value or token

                        # 调用API进行分析
                        result, _, raw_response = profile_api_call(ecg_signal, sampling_rate, current_token)

                        # 处理API结果
                        if result:
                            arrhythmia_diagnosis = result.get('ArrhythmiaDiagnosis', {})
                            diseases = get_disease_name(arrhythmia_diagnosis)
                            multi_label_diagnosis = result.get('MultiLabelDiagnosis', [])
                            chinese_multi_label = get_multi_label_disease_name(multi_label_diagnosis)

                            # 更新日志输出
                            logging.info(f"{row_label} 诊断结果:")
                            logging.info(f"  传统诊断结果: {diseases}")
                            logging.info(f"  多结论诊断结果: {', '.join(chinese_multi_label)}")
                            logging.info(f"    ECG年龄: {result.get('ECGAge')}")
                            pqrstc = result.get('PQRSTC', {})
                            logging.info("  PQRSTC参数:")
                            logging.info(f"    心率(HR): {pqrstc.get('HR', 0)} bpm")
                            qrs_duration = pqrstc.get('QRS_duration', pqrstc.get('QRS_QRSDuration', 0))
                            logging.info(f"    QRS时长: {qrs_duration} ms")

                            signal_quantity = result.get('SignalQuantity')
                            is_noise = result.get('IsNoise', False)
                            noise_message = result.get('NoiseMessage', '')
                            first_10s_skipped = noise_message.startswith("前10秒信号可能为噪音")
                            logging.info("  信号质量信息:")
                            logging.info(f"    信号质量: {signal_quantity}")
                            logging.info(f"    是否噪音: {'是' if is_noise else '否'}")
                            if noise_message:
                                logging.info(f"    噪音信息: {noise_message}")
                                
                            # 移除原始响应中的ecgAnalysis字段输出代码
                            
                            logging.info("-" * 50)

                            new_row = {
                                'Row': original_row_index_in_file + 1,  # Use original_row_index_in_file
                                'ID': record_id,
                                'ECGAge': result.get('ECGAge'),
                                'ArrhythmiaDiagnosis': result.get('ArrhythmiaDiagnosis', {}),
                                'MultiLabelDiagnosis': chinese_multi_label,
                                'HealthMetrics': result.get('HealthMetrics', {}),
                                'PQRSTC': result.get('PQRSTC', {}),
                                'SignalQuantity': signal_quantity,
                                'IsNoise': is_noise,
                                'NoiseMessage': noise_message,
                                'Status': '成功',
                                'Diseases': diseases,
                                'First10sSkipped': first_10s_skipped,
                                'RawResponse': raw_response,  # 保存完整原始API响应
                                'ErrorMessage': ''
                            }
                        else:
                            # 获取API错误详细信息
                            error_info = ""
                            if raw_response:
                                error_code = raw_response.get("code")
                                error_msg = raw_response.get("msg", "")
                                error_info = f"API返回错误: 代码={error_code}, 消息={error_msg}"
                            
                            logging.warning(f"{row_label}: API处理失败 - {error_info}")
                            new_row = {col: None for col in cols}
                            new_row.update({
                                'Row': original_row_index_in_file + 1,  # Use original_row_index_in_file
                                'ID': record_id,
                                'Status': '失败',
                                'ErrorMessage': error_info or 'API调用失败或返回错误',
                                'First10sSkipped': None,
                                'RawResponse': raw_response  # 保存原始错误响应
                            })
                        results_list.append(new_row)

                    except Exception as e:
                        logging.error(f"{row_label}: 处理时发生意外错误: {str(e)}")
                        error_row = {col: None for col in cols}
                        error_row.update({
                            'Row': original_row_index_in_file + 1,  # Use original_row_index_in_file
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': f'处理错误: {str(e)}',
                            'First10sSkipped': None
                        })
                        results_list.append(error_row)

        except FileNotFoundError:
            logging.error(f"CSV输入文件未找到: {input_file}")
            return pd.DataFrame(columns=cols)
        except pd.errors.EmptyDataError:
            logging.error(f"CSV输入文件为空: {input_file}")
            return pd.DataFrame(columns=cols)
        except Exception as e:
            logging.error(f"读取或处理CSV文件 {input_file} 时发生严重错误: {str(e)}")
            return pd.DataFrame(columns=cols)
    elif file_extension == '.json':
        logging.info(f"开始处理JSON文件: {input_file}")
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                json_data_list = json.load(f)  # Expecting a list of records

            if not isinstance(json_data_list, list):
                logging.error(f"JSON文件 {input_file} 内容不是预期的列表格式。实际类型: {type(json_data_list)}")
                # If it's a single record, wrap it in a list to process it
                if isinstance(json_data_list, dict):
                    logging.info(f"检测到JSON文件为单个记录对象，将作为单元素列表处理。")
                    json_data_list = [json_data_list]
                else:
                    return pd.DataFrame(columns=cols)

            for record_idx, record in enumerate(json_data_list):
                row_label = f"JSON 文件 '{os.path.basename(input_file)}', 记录 {record_idx + 1}"
                record_id = record.get("id", f"记录{record_idx + 1}")
                # study_uid = record.get("study_uid") # Optional: log or include if needed

                try:
                    ecg_data_str = record.get("ecg_data")
                    ecg_data = []

                    if isinstance(ecg_data_str, str) and ecg_data_str.startswith('[') and ecg_data_str.endswith(']'):
                        ecg_data_str_trimmed = ecg_data_str[1:-1]
                        data_points_str = ecg_data_str_trimmed.split(',')
                        for point_str in data_points_str:
                            try:
                                ecg_data.append(float(point_str.strip()))
                            except ValueError:
                                logging.warning(f"{row_label}: 无法将数据点 '{point_str}' 转换为浮点数，已跳过")
                    else:
                        logging.warning(f"{row_label}: 'ecg_data' 字段不是预期的字符串列表格式或不存在: {ecg_data_str}")

                    if not ecg_data:
                        logging.warning(f"{row_label}: ECG数据为空或无效")
                        error_row = {col: None for col in cols}
                        error_row.update({
                            'Row': record_idx + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': 'ECG数据为空或无效'
                        })
                        results_list.append(error_row)
                        continue

                    min_required_samples = sampling_rate * 10
                    if len(ecg_data) < min_required_samples:
                        logging.warning(
                            f"{row_label}: 数据长度不足10秒 ({len(ecg_data)} < {min_required_samples})，跳过")
                        error_row = {col: None for col in cols}
                        error_row.update({
                            'Row': record_idx + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': f'数据长度不足10秒({len(ecg_data)}<{min_required_samples})'
                        })
                        results_list.append(error_row)
                        continue

                    ecg_signal = ecg_data
                    logging.info(f"{row_label}: 准备发送 {len(ecg_signal)} 个样本点给 API")

                    current_token = token_value or token
                    result, _, raw_response = profile_api_call(ecg_signal, sampling_rate, current_token)

                    if result:
                        arrhythmia_diagnosis = result.get('ArrhythmiaDiagnosis', {})
                        diseases = get_disease_name(arrhythmia_diagnosis)
                        multi_label_diagnosis = result.get('MultiLabelDiagnosis', [])
                        chinese_multi_label = get_multi_label_disease_name(multi_label_diagnosis)
                        signal_quantity = result.get('SignalQuantity')
                        is_noise = result.get('IsNoise', False)
                        noise_message = result.get('NoiseMessage', '')
                        first_10s_skipped = noise_message.startswith("前10秒信号可能为噪音")

                        logging.info(f"{row_label} 诊断结果:")
                        logging.info(f"  传统诊断结果: {diseases}")
                        logging.info(
                            f"  多结论诊断结果: {', '.join(chinese_multi_label) if chinese_multi_label else '无'}")
                            
                        # 移除原始响应中的ecgAnalysis字段输出代码

                        new_row = {
                            'Row': record_idx + 1,
                            'ID': record_id,
                            'ECGAge': result.get('ECGAge'),
                            'ArrhythmiaDiagnosis': result.get('ArrhythmiaDiagnosis', {}),
                            'MultiLabelDiagnosis': chinese_multi_label,
                            'HealthMetrics': result.get('HealthMetrics', {}),
                            'PQRSTC': result.get('PQRSTC', {}),
                            'SignalQuantity': signal_quantity,
                            'IsNoise': is_noise,
                            'NoiseMessage': noise_message,
                            'Status': '成功',
                            'Diseases': diseases,
                            'First10sSkipped': first_10s_skipped,
                            'RawResponse': raw_response,  # 保存完整原始API响应
                            'ErrorMessage': ''
                        }
                    else:
                        # 获取API错误详细信息
                        error_info = ""
                        if raw_response:
                            error_code = raw_response.get("code")
                            error_msg = raw_response.get("msg", "")
                            error_info = f"API返回错误: 代码={error_code}, 消息={error_msg}"
                        
                        logging.warning(f"{row_label}: API处理失败 - {error_info}")
                        new_row = {col: None for col in cols}
                        new_row.update({
                            'Row': record_idx + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': error_info or 'API调用失败或返回错误',
                            'RawResponse': raw_response  # 保存原始错误响应
                        })
                    results_list.append(new_row)

                except Exception as e:
                    logging.error(f"{row_label}: 处理单条JSON记录时发生意外错误: {str(e)}")
                    error_row = {col: None for col in cols}
                    error_row.update({
                        'Row': record_idx + 1,
                        'ID': record_id,
                        'Status': '失败',
                        'ErrorMessage': f'处理JSON记录错误: {str(e)}'
                    })
                    results_list.append(error_row)

        except FileNotFoundError:
            logging.error(f"JSON输入文件未找到: {input_file}")
            return pd.DataFrame(columns=cols)
        except json.JSONDecodeError as e:
            logging.error(f"解析JSON文件 {input_file} 失败: {str(e)}")
            return pd.DataFrame(columns=cols)
        except Exception as e:
            logging.error(f"读取或处理JSON文件 {input_file} 时发生严重错误: {str(e)}")
            return pd.DataFrame(columns=cols)

    else:
        logging.error(f"不支持的文件类型: {file_extension} (来自文件: {input_file})")
        return pd.DataFrame(columns=cols)

    # 转换为DataFrame并返回 (common to all paths that produce results_list)
    if not results_list:
        logging.warning("没有成功处理任何数据行")
        return pd.DataFrame(columns=cols)  # 确保返回带列名的空表

    df = pd.DataFrame(results_list)
    # 更新列顺序以包含新字段
    cols = [
        'Row', 'ID', 'Status', 'First10sSkipped', 'Diseases', 'MultiLabelDiagnosis', 'ECGAge',
        'ArrhythmiaDiagnosis', 'PQRSTC', 'HealthMetrics',
        # Signal quality相关列
        'SignalQuantity', 'IsNoise', 'NoiseMessage',
        'RawResponse', # 添加到列顺序中
        'ErrorMessage'
    ]
    df = df.reindex(columns=cols)

    # 打印成功率统计
    total = len(df)
    success = len(df[df['Status'] == '成功'])
    noise_count = len(df[df['IsNoise'] == True])
    logging.info(f"\n处理统计:")
    logging.info(f"总数: {total}")
    logging.info(f"成功: {success}")
    logging.info(f"失败: {total - success}")
    logging.info(f"噪音信号数: {noise_count}")
    if total > 0:
        logging.info(f"成功率: {(success / total * 100):.2f}%")
        logging.info(f"噪音比例: {(noise_count / total * 100):.2f}%")
    else:
        logging.info("成功率: N/A")

    return df


def main():
    # 创建主性能分析器
    main_profiler = cProfile.Profile()  # Re-enabled
    main_profiler.enable()  # Re-enabled

    try:
        # 获取命令行参数
        args = parse_arguments()

        # 如果没有命令行参数，使用交互式输入
        if args.mode is None:
            print("请选择处理模式：")
            print("1: 单个文件")
            print("2: 整个文件夹")
            print("3: 七牛云单个es_key诊断")
            print("4: 七牛云批量es_key诊断")
            print("5: Excel文件es_key诊断")
            mode = input("请输入模式编号（1-5）：")
            mode = int(mode)
        else:
            mode = args.mode

        # 根据是否启用调试模式设置日志级别
        if args.debug:
            logging.getLogger().setLevel(logging.DEBUG)
            logging.info("已启用调试模式")
        else:
            logging.getLogger().setLevel(logging.INFO)

        # 移除错误代码说明的输出
        # logging.info("错误代码说明:")
        # for code, message in ERROR_CODE_MAP.items():
        #     logging.info(f"  - 代码 {code}: {message}")

        # 使用固定Sampling rate500Hz
        sampling_rate = 500
        logging.info(f"使用固定采样率: {sampling_rate} Hz")
        token = get_token(force_refresh=True)  # 强制刷新获取新token
        if not token:
            logging.error("无法获取到有效的token，程序结束")
            sys.exit(1)

        # 记录开始时间，用于定期检查token是否需要刷新
        process_start_time = time.time()

        if mode == 1:
            input_file = args.input or input("请输入要处理的CSV文件完整路径：").strip('"')
            if not os.path.exists(input_file):
                logging.error("文件不存在！")
                sys.exit(1)

            # 检测文件类型并输出相关信息
            file_extension = os.path.splitext(input_file)[1].lower()
            if file_extension in ['.h5', '.hdf5']:
                logging.info(f"检测到HDF5文件: {input_file}")
                # 尝试预览HDF5文件结构
                try:
                    with h5py.File(input_file, 'r') as h5f:
                        # 输出HDF5文件的顶级结构
                        logging.info("HDF5文件结构预览:")
                        for key in h5f.keys():
                            item = h5f[key]
                            if isinstance(item, h5py.Dataset):
                                logging.info(f"  - 数据集: {key}, 形状: {item.shape}, 类型: {item.dtype}")
                            elif isinstance(item, h5py.Group):
                                logging.info(f"  - 组: {key}")
                except Exception as e:
                    logging.error(f"预览HDF5文件结构时出错: {str(e)}")

            output_folder = os.path.dirname(input_file)
            file_name = os.path.basename(input_file)
            output_file_name = f"{os.path.splitext(file_name)[0]}_acc.csv"
            output_file_path = os.path.join(output_folder, output_file_name)

            results_df = process_single_file(input_file, sampling_rate, token)

            # 保存CSV文件，确保RawResponse列完整保存
            try:
                # 使用更安全的方式保存，避免API响应中的特殊字符导致问题
                results_df.to_csv(output_file_path, index=False, encoding='utf-8-sig', quoting=1)
                logging.info(f"分析结果已保存到: {output_file_path}")
            except Exception as e:
                logging.error(f"保存CSV文件时出错: {str(e)}")
                # 额外保存一个JSON格式的文件，以便更容易查看完整的API响应
                json_output_path = os.path.join(output_folder, f"{os.path.splitext(file_name)[0]}_acc.json")
                
                # 将DataFrame转换为可序列化的字典列表
                results_list = []
                for _, row in results_df.iterrows():
                    row_dict = row.to_dict()
                    # 确保所有内容都可以被JSON序列化
                    for k, v in row_dict.items():
                        if pd.isna(v):
                            row_dict[k] = None
                    results_list.append(row_dict)
                
                # 保存为JSON文件
                with open(json_output_path, 'w', encoding='utf-8') as f:
                    # 简化输出，只保留关键信息
                    simplified_results = []
                    for item in results_list:
                        if 'RawResponse' in item and item['RawResponse']:
                            # 从RawResponse中提取关键信息
                            try:
                                raw_resp = item['RawResponse']
                                if isinstance(raw_resp, str):
                                    raw_resp = json.loads(raw_resp)
                                
                                # 保留data部分但移除可能的大型数据字段
                                if 'data' in raw_resp:
                                    data = raw_resp['data']
                                    if 'ecgAnalysis' in data:
                                        # 只保留ecgAnalysis的关键部分
                                        if isinstance(data['ecgAnalysis'], dict):
                                            keys_to_keep = ['diagnosis', 'rhythmDiagnosis', 'features']
                                            data['ecgAnalysis'] = {k: v for k, v in data['ecgAnalysis'].items() if k in keys_to_keep}
                                        else:
                                            # 如果不是字典，则只保留字符串的前200个字符
                                            data['ecgAnalysis'] = str(data['ecgAnalysis'])[:200] + "..."
                                    
                                    # 更新简化后的RawResponse
                                    item['RawResponse'] = {'data': data, 'code': raw_resp.get('code')}
                            except Exception as e:
                                logging.warning(f"简化JSON输出时出错: {str(e)}")
                        
                        simplified_results.append(item)
                    
                    json.dump(simplified_results, f, ensure_ascii=False, indent=2)
                logging.info(f"简化JSON格式结果已保存到: {json_output_path}")
                
            except Exception as e:
                logging.error(f"保存结果时出错: {str(e)}")
                # 尝试使用更简单的方式保存
                results_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')

        elif mode == 3:
            # 七牛云es_key诊断模式
            es_key = args.input or input("请输入要诊断的es_key：").strip()
            environment = args.environment or input("请选择七牛云环境（test/prod，默认prod）：").strip() or 'prod'

            if not es_key:
                logging.error("es_key不能为空！")
                sys.exit(1)

            logging.info(f"开始七牛云es_key诊断模式")
            logging.info(f"es_key: {es_key}")
            logging.info(f"环境: {environment}")

            # 处理es_key诊断
            result = process_es_key_diagnosis(es_key, environment, sampling_rate, token)

            # 输出结果
            logging.info("\n=== 诊断结果 ===")
            for key, value in result.items():
                if key not in ['raw_response', 'qiniu_data']:  # 跳过大型数据字段
                    logging.info(f"{key}: {value}")

            # 保存结果到JSON文件
            output_file = f"es_key_diagnosis_{es_key.replace('/', '_')}.json"
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2, default=str)
                logging.info(f"详细诊断结果已保存到: {output_file}")
            except Exception as e:
                logging.error(f"保存结果文件时出错: {e}")

        elif mode == 4:
            # 七牛云批量es_key诊断模式
            environment = args.environment or input("请选择七牛云环境（test/prod，默认prod）：").strip() or 'prod'
            output_dir = args.output or input("请输入输出目录（默认当前目录）：").strip() or "."

            # 获取es_key列表
            es_key_list = []

            if args.es_keys:
                # 从命令行参数获取
                es_key_list = args.es_keys
                logging.info(f"从命令行参数获取到 {len(es_key_list)} 个es_key")
            elif args.input:
                # 从文件获取
                input_file = args.input
                if os.path.exists(input_file):
                    try:
                        if input_file.endswith('.csv'):
                            # CSV文件
                            df = pd.read_csv(input_file)
                            if 'es_key' in df.columns:
                                es_key_list = df['es_key'].dropna().tolist()
                            else:
                                logging.error("CSV文件中未找到'es_key'列")
                                sys.exit(1)
                        elif input_file.endswith('.txt'):
                            # 文本文件，每行一个es_key
                            with open(input_file, 'r', encoding='utf-8') as f:
                                es_key_list = [line.strip() for line in f if line.strip()]
                        elif input_file.endswith('.json'):
                            # JSON文件
                            with open(input_file, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                if isinstance(data, list):
                                    es_key_list = data
                                elif isinstance(data, dict) and 'es_keys' in data:
                                    es_key_list = data['es_keys']
                                else:
                                    logging.error("JSON文件格式不正确")
                                    sys.exit(1)
                        else:
                            logging.error("不支持的文件格式，请使用CSV、TXT或JSON文件")
                            sys.exit(1)

                        logging.info(f"从文件 {input_file} 读取到 {len(es_key_list)} 个es_key")
                    except Exception as e:
                        logging.error(f"读取文件失败: {e}")
                        sys.exit(1)
                else:
                    logging.error(f"文件不存在: {input_file}")
                    sys.exit(1)
            else:
                # 交互式输入
                print("请输入es_key列表（支持以下方式）：")
                print("1. 直接输入多个es_key，用逗号或换行分隔")
                print("2. 输入文件路径（CSV/TXT/JSON格式）")
                user_input = input("请输入：").strip()

                if os.path.exists(user_input):
                    # 用户输入的是文件路径，重新设置参数并处理
                    input_file = user_input
                    try:
                        if input_file.endswith('.csv'):
                            df = pd.read_csv(input_file)
                            if 'es_key' in df.columns:
                                es_key_list = df['es_key'].dropna().tolist()
                            else:
                                logging.error("CSV文件中未找到'es_key'列")
                                sys.exit(1)
                        elif input_file.endswith('.txt'):
                            with open(input_file, 'r', encoding='utf-8') as f:
                                es_key_list = [line.strip() for line in f if line.strip()]
                        elif input_file.endswith('.json'):
                            with open(input_file, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                if isinstance(data, list):
                                    es_key_list = data
                                elif isinstance(data, dict) and 'es_keys' in data:
                                    es_key_list = data['es_keys']
                                else:
                                    logging.error("JSON文件格式不正确")
                                    sys.exit(1)
                        else:
                            logging.error("不支持的文件格式")
                            sys.exit(1)
                        logging.info(f"从文件 {input_file} 读取到 {len(es_key_list)} 个es_key")
                    except Exception as e:
                        logging.error(f"读取文件失败: {e}")
                        sys.exit(1)
                else:
                    # 用户直接输入es_key
                    if ',' in user_input:
                        es_key_list = [key.strip() for key in user_input.split(',') if key.strip()]
                    else:
                        es_key_list = [line.strip() for line in user_input.split('\n') if line.strip()]

            if not es_key_list:
                logging.error("未获取到任何es_key！")
                sys.exit(1)

            logging.info(f"开始批量七牛云es_key诊断模式")
            logging.info(f"es_key数量: {len(es_key_list)}")
            logging.info(f"环境: {environment}")
            logging.info(f"输出目录: {output_dir}")

            # 批量处理es_key诊断
            results = process_es_key_batch(es_key_list, environment, sampling_rate, token, output_dir)

            # 输出统计结果
            success_count = sum(1 for r in results if r['status'] == '成功')
            failed_count = len(results) - success_count

            logging.info(f"\n=== 批量诊断完成 ===")
            logging.info(f"总计: {len(results)}")
            logging.info(f"成功: {success_count}")
            logging.info(f"失败: {failed_count}")
            logging.info(f"成功率: {success_count/len(results)*100:.1f}%")

        elif mode == 5:
            # Excel文件es_key诊断模式
            excel_file = args.input or input("请输入Excel文件路径（.xls或.xlsx）：").strip().strip('"')
            environment = args.environment or input("请选择七牛云环境（test/prod，默认prod）：").strip() or 'prod'
            output_dir = args.output or input("请输入输出目录（默认当前目录）：").strip() or "."

            if not excel_file:
                logging.error("Excel文件路径不能为空！")
                sys.exit(1)

            if not os.path.exists(excel_file):
                logging.error(f"Excel文件不存在: {excel_file}")
                sys.exit(1)

            if not (excel_file.endswith('.xls') or excel_file.endswith('.xlsx')):
                logging.error("请提供有效的Excel文件（.xls或.xlsx格式）")
                sys.exit(1)

            logging.info(f"开始Excel文件es_key诊断模式")
            logging.info(f"Excel文件: {excel_file}")
            logging.info(f"环境: {environment}")
            logging.info(f"输出目录: {output_dir}")

            try:
                # 读取Excel文件
                logging.info("正在读取Excel文件...")
                df = pd.read_excel(excel_file)
                logging.info(f"Excel文件读取成功，共 {len(df)} 行数据")
                logging.info(f"Excel文件列名: {df.columns.tolist()}")

                # 检查es_key列
                if 'es_key' not in df.columns:
                    logging.error("Excel文件中未找到'es_key'列")
                    logging.info("可用的列名：" + ", ".join(df.columns.tolist()))
                    sys.exit(1)

                # 提取es_key列，去除空值
                es_key_series = df['es_key'].dropna()
                es_key_list = es_key_series.tolist()

                # 去除重复的es_key
                unique_es_keys = list(set(es_key_list))

                logging.info(f"从Excel文件提取到 {len(es_key_list)} 个es_key")
                logging.info(f"去重后有 {len(unique_es_keys)} 个唯一es_key")

                if not unique_es_keys:
                    logging.error("Excel文件中的es_key列为空或全部为空值")
                    sys.exit(1)

                # 显示前几个es_key作为预览
                preview_count = min(5, len(unique_es_keys))
                logging.info(f"前{preview_count}个es_key预览:")
                for i, es_key in enumerate(unique_es_keys[:preview_count], 1):
                    logging.info(f"  {i}. {es_key}")

                # 确认是否继续
                if not args.input:  # 只有在交互模式下才询问确认
                    confirm = input(f"\n确认要处理这 {len(unique_es_keys)} 个es_key吗？(y/N): ").strip().lower()
                    if confirm not in ['y', 'yes']:
                        logging.info("用户取消操作")
                        sys.exit(0)

                # 批量处理es_key诊断
                results = process_es_key_batch(unique_es_keys, environment, sampling_rate, token, output_dir)

                # 输出统计结果
                success_count = sum(1 for r in results if r['status'] == '成功')
                failed_count = len(results) - success_count

                logging.info(f"\n=== Excel文件es_key诊断完成 ===")
                logging.info(f"Excel文件: {os.path.basename(excel_file)}")
                logging.info(f"总计: {len(results)}")
                logging.info(f"成功: {success_count}")
                logging.info(f"失败: {failed_count}")
                logging.info(f"成功率: {success_count/len(results)*100:.1f}%")

                # 保存带有诊断结果的Excel文件
                try:
                    # 创建结果DataFrame
                    result_df = pd.DataFrame(results)

                    # 与原始Excel数据合并
                    merged_df = df.merge(result_df, on='es_key', how='left')

                    # 保存合并后的结果
                    output_excel = os.path.join(output_dir, f"diagnosed_{os.path.basename(excel_file)}")
                    merged_df.to_excel(output_excel, index=False)
                    logging.info(f"带诊断结果的Excel文件已保存到: {output_excel}")

                except Exception as e:
                    logging.error(f"保存合并Excel文件时出错: {e}")

            except Exception as e:
                logging.error(f"处理Excel文件时发生错误: {e}")
                import traceback
                logging.error(traceback.format_exc())
                sys.exit(1)

        elif mode == 2:
            input_folder = args.input or input("请输入要处理的文件夹路径：").strip('"')
            output_folder = args.output or input("请输入结果保存的文件夹路径：").strip('"')

            if not os.path.exists(input_folder):
                logging.error("输入文件夹不存在！")
                sys.exit(1)

            if not os.path.exists(output_folder):
                os.makedirs(output_folder)

            supported_extensions = ('.csv', '.h5', '.hdf5', '.json')
            all_files = [f for f in os.listdir(input_folder) if f.lower().endswith(supported_extensions)]

            # 按文件类型分组统计
            file_type_count = {}
            for ext in supported_extensions:
                count = len([f for f in all_files if f.lower().endswith(ext)])
                if count > 0:
                    file_type_count[ext] = count

            logging.info(f"在文件夹 '{input_folder}' 中找到以下支持的文件：")
            for ext, count in file_type_count.items():
                logging.info(f"  - {ext} 文件: {count} 个")
            logging.info(f"总文件数: {len(all_files)}")

            if not all_files:
                logging.warning(f"在文件夹 '{input_folder}' 中没有找到支持的文件（{', '.join(supported_extensions)}）。")
                sys.exit(0)  # Graceful exit if no files to process

            # === 新增全局统计变量 ===
            total_count = 0
            success_count = 0
            fail_count = 0
            noise_count = 0

            for data_file in all_files:
                input_file_path = os.path.join(input_folder, data_file)
                output_file_name = f"{os.path.splitext(data_file)[0]}_acc.csv"
                output_file_path = os.path.join(output_folder, output_file_name)

                # 每个文件处理前检查token是否需要刷新
                current_time = time.time()
                if current_time - token_obtain_time > TOKEN_REFRESH_THRESHOLD:
                    logging.info("Token即将过期，尝试刷新")
                    new_token = get_token(force_refresh=True)
                    if new_token:
                        token = new_token

                logging.info(f"开始处理文件：{data_file} (保存到: {output_file_path})")
                results_df = process_single_file(input_file_path, sampling_rate, token)
                results_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
                logging.info(f"文件 {data_file} 的分析结果已保存到: {output_file_path}")

                # === 累加统计 ===
                file_total = len(results_df)
                file_success = len(results_df[results_df['Status'] == '成功'])
                file_fail = file_total - file_success
                file_noise = len(results_df[results_df['IsNoise'] == True])
                total_count += file_total
                success_count += file_success
                fail_count += file_fail
                noise_count += file_noise

            # === 所有文件处理完后统一输出统计 ===
            logging.info("\n文件夹整体处理统计:")
            logging.info(f"总数: {total_count}")
            logging.info(f"成功: {success_count}")
            logging.info(f"失败: {fail_count}")
            logging.info(f"噪音信号数: {noise_count}")
            if total_count > 0:
                logging.info(f"成功率: {(success_count / total_count * 100):.2f}%")
                logging.info(f"噪音比例: {(noise_count / total_count * 100):.2f}%")
            else:
                logging.info("成功率: N/A")

        else:
            logging.error("无效的选择！请选择1、2、3、4或5")
            sys.exit(1)

    finally:
        main_profiler.disable()  # Re-enabled

        # 创建Stats对象并提取摘要信息
        s = io.StringIO()
        stats = pstats.Stats(main_profiler, stream=s)
        stats.sort_stats(SortKey.TIME)
        # stats.print_stats() # Keep this commented if only summary is needed for logging

        # 从 stats 对象中提取所需信息
        # 注意: pstats 的内部结构可能变化，这里是基于常见属性的访问方式
        total_calls = stats.total_calls
        primitive_calls = stats.prim_calls
        total_tt = stats.total_tt

        logging.info(
            f"性能摘要: {total_calls} function calls ({primitive_calls} primitive calls) in {total_tt:.3f} seconds")

        # 保存完整的统计信息到文件 (可选)
        stats_file = "api_test_performance.txt"  # Re-enabled
        with open(stats_file, 'w', encoding='utf-8') as f:  # Re-enabled
            # 重新创建stats对象并将流指向文件
            stats_to_file = pstats.Stats(main_profiler, stream=f)  # Re-enabled
            stats_to_file.sort_stats(SortKey.TIME)  # Re-enabled
            stats_to_file.print_stats()  # Re-enabled

        logging.info(f"完整的性能分析结果已保存到: {stats_file}")  # Re-enabled
        logging.info("处理完成。")  # 更改为中文的完成消息


if __name__ == '__main__':
    main()