from datetime import datetime

from apps.models.ecg_analysis_modes import TBreatheInfo


def process(union_id, data):
    save_data(union_id, data)


def save_data(union_id, data):
    """
    保存数据
    :param union_id:
    :param data:
    :return:
    """
    breathe_info = TBreatheInfo()
    breathe_info.union_id = union_id
    breathe_info.breathe_data = data
    breathe_info.create_date = datetime.now()
    breathe_info.save()
