import ast
import json
import traceback

import numpy as np
from django.utils.decorators import method_decorator
from django.views import View

import apps.analysis.arrhythmia_diagnosis.diagnosis as arrhythmia_diagnosis_diagnosis
import apps.analysis.cad_cardiomyopathy.diagnosis as cad_cardiomyopathy_diagnosis
import apps.analysis.health_metrics.diagnosis as health_metrics_diagnosis
import apps.analysis.pqrstc.diagnosis as pqrstc_diagnosis
from apps.analysis import whitelist
from apps.analysis.common.custom_exception import BiosppyEcgError
from apps.analysis.common.data_filter import filter_negative_values
from apps.analysis.diagnosis_filter.filter import apply_rules
from apps.analysis.ecg_age import diagnosis as ecg_age_diagnosis
from apps.api.diagnose.business import save_api_log, get_diagnosis_details
from apps.models.analysis_models import AnalysisEntity, ArrhythmiaDiagnosisEntity, HealthMetricsEntity, MotionInfoEntity
from apps.signal_analysis.available import SignalAnalysis
from apps.signal_analysis.waveform import get_waveform
from apps.utils.decorator import authentication
from apps.utils.get_response import GetResponse
from apps.utils.logger_helper import Logger
from apps.analysis.gravity.business import process as gravity_process
from apps.analysis.breathe.business import process as breathe_process
from apps.analysis.breathe.breathe_analyzer import analyze_breathe_data
from apps.analysis.breathe.sleep_infer import run_sleep_inference


@method_decorator(authentication, name="dispatch")
class ArrhythmiaView(View):
    """
    心率失常诊断
    """

    def post(self, request, *args, **kwargs):
        """
        POST请求接口
        :param request: 请求对象
        :return: 诊断结果
        """
        custom_id = kwargs.get('custom_id')  # 默认给一个整数ID，避免错误
        ecg_data = None
        union_id = None

        param_list = ['signal', 'fs', 'adc_gain', 'adc_zero', 'union_id', 'ecg_age_key', 'health_metrics']

        try:
            data = json.loads(request.body)

            # 验证参数是否存在
            missing_params = [param for param in param_list if param not in data]

            if len(missing_params) > 0:
                return GetResponse.get_response(code=6)

            ecg_data_str = data['signal']  # ecg信号

            if isinstance(ecg_data_str, list):
                ecg_data = np.array(data['signal'])
            elif ecg_data_str.find('[') != -1 and ecg_data_str.find(']') != -1:
                ecg_data = np.array(ast.literal_eval(f"{ecg_data_str}"))
            else:
                ecg_data = np.array(ast.literal_eval(f"[{ecg_data_str}]"))  # 将心电信号转为nparray

            sampling_rate = data['fs']  # 采样率
            gain = data['adc_gain']  # 增益
            zero = data['adc_zero']  # 零点（基线）
            union_id = data['union_id']  # 用户ID
            ecg_age_key = data['ecg_age_key']  # 心脏年龄 0-不需要计算 1-需要计算
            health_metrics = data['health_metrics']  # 情绪指数 0-不需要计算 1-需要计算

            sampling_rate = int(sampling_rate)

            if len(ecg_data) < sampling_rate * 10:
                return GetResponse.get_response(code=6)

            if ecg_data is None or sampling_rate is None or gain is None or zero is None or gain <= 0:
                return GetResponse.get_response(code=5)

            if not isinstance(sampling_rate, int):
                return GetResponse.get_response(code=6)

            ecg_data = (ecg_data - zero) / gain  # 计算实际电压（检测电压-基线）/ 增益

            # 分析实体对象
            analysis_entity = AnalysisEntity()

            # 获取可用信号
            signal_analysis = SignalAnalysis(ecg_data, sampling_rate)
            signal_quality, normal_time_period = signal_analysis.get_available_signals()

            if signal_quality == -2:
                return GetResponse.get_response(code=8)
            elif signal_quality == -3:
                return GetResponse.get_response(code=9)

            analysis_entity.SignalQuantity = signal_quality  # 信号质量 1-正常,-1-噪声
            signal_time_period = normal_time_period[0]  # 默认取第一个时间段
            analysis_entity.SignalTimePeriod = f'{signal_time_period[0]}-{signal_time_period[1]}'

            # 数据切割，只处理有效信号段
            ecg_data_processed = ecg_data[signal_time_period[0] * sampling_rate: signal_time_period[1] * sampling_rate]

            waveform_info = get_waveform(ecg_data_processed, sampling_rate)  # 波形分析
            if waveform_info is None:
                return GetResponse.get_response(code=7)

            # 设置白名单
            whitelist_arrhythmia_diagnosis, whitelist_ecg_age = None, None

            # 非噪音读取白名单信息
            if analysis_entity.SignalQuantity != -1:
                whitelist_arrhythmia_diagnosis, whitelist_ecg_age = whitelist.process(union_id)

            if ecg_age_key == 1:
                # 如果白名单中设置了年龄优先使用白名单年龄
                if whitelist_ecg_age:
                    analysis_entity.ECGAge = whitelist_ecg_age
                else:
                    analysis_entity.ECGAge = ecg_age_diagnosis.process(ecg_data_processed, sampling_rate)  # 心脏年龄

            analysis_entity.HeartFailureRisk = 0  # 心衰风险（0-1）
            analysis_entity.VentricularFibrillationRisk = 0  # 室颤风险（0-1）
            analysis_entity.SyncopeRisk = 0  # 晕厥风险（0-1）
            # 睡眠相关
            analysis_entity.SleepStage = 0  # 默认清醒，睡眠阶段 取值范围为（0，1，2，3，4），对应睡眠阶段（Wake，N1， N2， N3, REM）
            analysis_entity.OSARisk = -1  # 默认-1（时长不足），阻塞性睡眠呼吸暂停风险，取值范围为（0-1）, -1代表时长小于一分钟
            analysis_entity.RespiratoryRate = 0  # 默认0，呼吸次数/min 取值范围[10-25]

            pqrstc = pqrstc_diagnosis.process(waveform_info)  # ECG信号指标

            # 修改条件：对非噪声信号(0和1)执行完整诊断，仅对噪声信号(-1)执行基础诊断
            if analysis_entity.SignalQuantity != -1:  # 正常信号或异常信号处理
                if whitelist_arrhythmia_diagnosis:
                    analysis_entity.ArrhythmiaDiagnosis = whitelist_arrhythmia_diagnosis
                else:
                    analysis_entity.ArrhythmiaDiagnosis = arrhythmia_diagnosis_diagnosis.process(ecg_data_processed,
                                                                                                 sampling_rate,
                                                                                                 waveform_info)  # 心率失常诊断
            else:
                # 只有在不是平直线+高频振荡噪声的情况下才进行心律诊断
                new_diagnosis = ArrhythmiaDiagnosisEntity()
                # 根据心率设置不同的心律诊断
                hr = pqrstc.HR if pqrstc and hasattr(pqrstc, 'HR') else 75  # 默认值75
                if hr > 100:
                    # 窦性心动过速
                    new_diagnosis.SNT = 1
                elif hr < 60:
                    # 窦性心动过缓
                    new_diagnosis.SNB = 1
                else:
                    # 窦性心律（正常）
                    new_diagnosis.SN = 1
                analysis_entity.ArrhythmiaDiagnosis = new_diagnosis

            analysis_entity.CADCardiomyopathy = cad_cardiomyopathy_diagnosis.process(waveform_info)  # 心肌病冠心病诊断

            if health_metrics == 1:
                analysis_entity.HealthMetrics = health_metrics_diagnosis.process(waveform_info)  # 健康指标
            else:
                analysis_entity.HealthMetrics = HealthMetricsEntity()

            analysis_entity.PQRSTC = pqrstc

            analysis_entity.RRIntervals = ','.join(map(str, waveform_info.get('waveform', {}).get('rr_intervals', [])))
            analysis_entity.NNIntervals = ','.join(map(str, waveform_info.get('waveform', {}).get('nn_intervals', [])))

            # 处理加速度
            if 'gravity' in data and data['gravity']:
                gravity = data['gravity']
                analysis_entity.MotionInfo = gravity_process(union_id, data['gravity'])
                
                # 将运动强度为中(2)和高(3)的状态定义为噪音
                if analysis_entity.MotionInfo and analysis_entity.MotionInfo.motion_intensity in [2, 3]:
                    analysis_entity.SignalQuantity = -1
            else:
                gravity = None
                analysis_entity.MotionInfo = MotionInfoEntity()

            # 处理呼吸波
            if 'breatheData' in data and data['breatheData']:
                # 呼吸波分析
                breathe = data['breatheData']
                breathe_fs = 250  # 呼吸波采样率
                Logger().info(f'检测到呼吸波数据，长度: {len(breathe)} 个样本点，采样率: {breathe_fs}Hz')

                # 保存原始数据
                breathe_process(union_id, breathe)

                # 分析呼吸频率
                analysis_entity.RespiratoryRate = analyze_breathe_data(breathe, breathe_fs)
                Logger().info(f'计算得到呼吸频率: {analysis_entity.RespiratoryRate} 次/分钟')

                # 睡眠检测（需要足够长的数据，建议>=2分钟）
                try:
                    min_duration_sec = 60  # 1分钟最小要求
                    ecg_duration = len(ecg_data_processed) / sampling_rate
                    Logger().info(f'ECG数据时长: {ecg_duration:.1f}秒，最小要求: {min_duration_sec}秒')

                    if ecg_duration >= min_duration_sec:
                        Logger().info('开始执行睡眠检测推理...')
                        sleep_result = run_sleep_inference(
                            ecg_data=ecg_data_processed,
                            ecg_fs=sampling_rate,
                            breathe_data=breathe,
                            breathe_fs=breathe_fs  # 使用统一的呼吸波采样率
                        )

                        if sleep_result['windows']:
                            # 使用最后一个完整窗口的结果
                            analysis_entity.SleepStage = sleep_result['aggregate']['pred']
                            Logger().info(f'睡眠检测完成: 睡眠阶段={analysis_entity.SleepStage}, 窗口数={len(sleep_result["windows"])}')

                            if ecg_duration >= 120:  # >=2分钟才计算OSA风险
                                # 基于呼吸特征计算OSA风险，暂时设为0
                                analysis_entity.OSARisk = 0.0
                            else:
                                analysis_entity.OSARisk = -1  # 时长不足
                            Logger().info(f'OSA风险评估: {analysis_entity.OSARisk}')
                        else:
                            analysis_entity.SleepStage = 0  # 默认清醒
                            analysis_entity.OSARisk = -1
                    else:
                        analysis_entity.SleepStage = 0  # 默认清醒
                        analysis_entity.OSARisk = -1  # 时长不足

                except Exception as sleep_error:
                    Logger().warning(f'Sleep analysis failed for union_id: {union_id}, error: {str(sleep_error)}')
                    analysis_entity.SleepStage = 0  # 默认清醒
                    analysis_entity.OSARisk = -1

            # 诊断详情设置
            analysis_entity.DiagnosisDetails = get_diagnosis_details(analysis_entity, waveform_info, sampling_rate)

            analysis_entity.ecg_id = save_api_log(ecg_data_str, gravity, sampling_rate, gain, zero, analysis_entity,
                                                  custom_id)  # ecg信号分析ID

            return GetResponse.get_response(code=0, data=self.after_process(analysis_entity))
        except BiosppyEcgError as e:
            # 错误处理
            Logger().error(f'\n客户ID: {union_id}, 心电信号：{ecg_data}\n{traceback.format_exc()}')
            return GetResponse.get_response(code=7)
        except Exception as e:
            # 错误处理
            Logger().error(f'\n客户ID: {union_id}, 心电信号：{ecg_data}\n{traceback.format_exc()}')
            return GetResponse.get_response(code=2)

    def after_process(self, analysis_entity):
        """
        后处理
        :param analysis_entity: 分析实体对象
        :return: 处理后的分析实体对象
        """

        # 规则过滤
        final_result = apply_rules(analysis_entity)

        if not any(value == 1 for value in vars(final_result.ArrhythmiaDiagnosis).values()):
            final_result.ArrhythmiaDiagnosis.SN = 1

        # 过滤负值并返回
        response_data = filter_negative_values(final_result.to_entity_dict())

        return response_data
